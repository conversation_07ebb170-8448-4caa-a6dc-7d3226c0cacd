from collections import deque
import threading
from PyQt5.QtCore import QObject, pyqtSignal

MAX_EVENTS = 25

class LiveFeedManager(QObject):
    feed_updated = pyqtSignal(list)

    def __init__(self):
        super().__init__()
        self.events = deque(maxlen=MAX_EVENTS)
        self.lock = threading.Lock()

    def add_event(self, event):
        with self.lock:
            self.events.appendleft(event)
            self.feed_updated.emit(list(self.events))

    def get_events(self):
        with self.lock:
            return list(self.events)

    def clear_events(self):
        with self.lock:
            self.events.clear()
            self.feed_updated.emit(list(self.events))

    def is_empty(self):
        with self.lock:
            return len(self.events) == 0

live_feed_manager = LiveFeedManager() 