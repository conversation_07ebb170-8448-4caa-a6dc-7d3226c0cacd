import platform

chromium_links = {
    "win64": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win_x64%2F1110125%2Fchrome-win.zip?generation=1677456389285817&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win_x64%2F1110125%2Fchromedriver_win32.zip?generation=1677456585621401&alt=media"
    },
    "win32": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win%2F1110036%2Fchrome-win.zip?generation=1677368775095363&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win%2F1110036%2Fchromedriver_win32.zip?generation=1677368988947504&alt=media"
    },
    "mac": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac%2F1110036%2Fchrome-mac.zip?generation=1677366724785907&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac%2F1110036%2Fchromedriver_mac64.zip?generation=1677366732746411&alt=media"
    },
    "mac_arm": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac_Arm%2F1110125%2Fchrome-mac.zip?generation=1677454456625954&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac_Arm%2F1110125%2Fchromedriver_mac64.zip?generation=1677454462117324&alt=media"
    },
    "linux_x64": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Linux_x64%2F1109848%2Fchrome-linux.zip?generation=1677278713298627&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Linux_x64%2F1109848%2Fchromedriver_linux64.zip?generation=1677278719174033&alt=media"
    }
}


def detect_platform_key():
    system = platform.system()
    arch = platform.architecture()[0]

    if system == "Windows":
        return "win64" if "64" in arch else "win32"
    elif system == "Darwin":
        machine = platform.machine().lower()
        return "mac_arm" if "arm" in machine else "mac"
    elif system == "Linux":
        return "linux_x64"
    else:
        raise Exception("Desteklenmeyen işletim sistemi")


TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "615971252"
