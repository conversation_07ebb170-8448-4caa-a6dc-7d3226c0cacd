#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Real Download Test with Live Feed
Bu dosya gerçek bir indirme işlemi ile live feed sistemini test eder.
"""

import time
import threading
from live_feed import live_feed_system, format_live_feed_html
from download import download_instagram_post_simple

def test_real_download():
    print("🔍 Gerçek İndirme ile Live Feed Testi...")
    
    # Test URL'si (genel erişilebilir bir Instagram postu)
    test_url = "https://www.instagram.com/p/C1234567890/"  # Bu gerçek bir URL olmalı
    
    print(f"\n📥 Test URL'si: {test_url}")
    print("⚠️  Not: Bu test gerçek bir Instagram URL'si gerektirir")
    
    # Live feed durumunu izlemek için thread
    def monitor_live_feed():
        for i in range(30):  # 30 saniye izle
            feed_data = live_feed_system.get_feed_data()
            active_count = len(feed_data.get('active_downloads', []))
            completed_count = len(feed_data.get('completed_downloads', []))
            
            print(f"[{i+1:2d}s] Aktif: {active_count}, Tamamlanan: {completed_count}")
            
            if active_count > 0:
                for download in feed_data.get('active_downloads', []):
                    platform = download.get('platform', 'Unknown')
                    username = download.get('username', 'Unknown')
                    progress = download.get('progress', 0)
                    print(f"      → {platform.title()}: @{username} (%{progress})")
            
            time.sleep(1)
    
    # İzleme thread'ini başlat
    monitor_thread = threading.Thread(target=monitor_live_feed, daemon=True)
    monitor_thread.start()
    
    print("\n🚀 İndirme başlatılıyor...")
    
    # Gerçek indirme işlemi
    try:
        success, shortcode, video_path, thumbnail_path, caption = download_instagram_post_simple(
            test_url, "test_output"
        )
        
        if success:
            print(f"\n✅ İndirme başarılı!")
            print(f"   Shortcode: {shortcode}")
            print(f"   Video: {video_path}")
            print(f"   Thumbnail: {thumbnail_path}")
        else:
            print(f"\n❌ İndirme başarısız!")
            
    except Exception as e:
        print(f"\n❌ İndirme hatası: {e}")
    
    # Son durum
    time.sleep(2)
    final_data = live_feed_system.get_feed_data()
    print(f"\n📊 Final Durum:")
    print(f"   Aktif indirmeler: {len(final_data.get('active_downloads', []))}")
    print(f"   Tamamlanan: {len(final_data.get('completed_downloads', []))}")
    
    # HTML çıktısı
    html_output = format_live_feed_html(final_data)
    print(f"\n📄 HTML Çıktı Uzunluğu: {len(html_output)} karakter")
    
    print("\n✅ Gerçek indirme testi tamamlandı!")

if __name__ == "__main__":
    test_real_download() 