import logging
from apscheduler.schedulers.background import BackgroundScheduler
from upload import schedule_all_profiles_uploads

def reload_scheduler_helper(obj):
    """
    obj: scheduler attribute'u olan bir nesne (ör: MainWindow veya ilgili context)
    Scheduler zaten çalışı<PERSON><PERSON> yeniden ba<PERSON>, sadece job list<PERSON><PERSON> g<PERSON>ller.
    """
    if not hasattr(obj, "scheduler"):
        obj.scheduler = BackgroundScheduler(
            timezone="Europe/Istanbul",
            job_defaults={
                "misfire_grace_time": 300,
                "coalesce": True,
                "max_instances": 1
            }
        )
    else:
        obj.scheduler.remove_all_jobs()

    schedule_all_profiles_uploads(obj.scheduler)

    # Sadece durmu<PERSON> başlat
    if not getattr(obj.scheduler, "running", False):
        obj.scheduler.start()
        logging.info("Scheduler başlatıldı.")
    else:
        logging.info("Scheduler güncellendi (yeniden başlatılmadı).") 