#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Live Feed Integration Check
Bu dosya Live Feed sisteminin tüm bileşenlerinin doğru entegre olduğunu kontrol eder.
"""

import os
import sys
import importlib.util

def check_file_exists(file_path, description):
    """Do<PERSON>anın var olup olmadığını kontrol eder"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - BULUNAMADI!")
        return False

def check_import(module_name, description):
    """Modülün import edilebilir olup olmadığını kontrol eder"""
    try:
        __import__(module_name)
        print(f"✅ {description}: {module_name}")
        return True
    except ImportError as e:
        print(f"❌ {description}: {module_name} - IMPORT HATASI: {e}")
        return False

def check_function_exists(module_name, function_name, description):
    """Modülde fonksiyonun var olup olmadığını kontrol eder"""
    try:
        module = __import__(module_name)
        if hasattr(module, function_name):
            print(f"✅ {description}: {module_name}.{function_name}")
            return True
        else:
            print(f"❌ {description}: {module_name}.{function_name} - FONKSİYON BULUNAMADI!")
            return False
    except Exception as e:
        print(f"❌ {description}: {module_name}.{function_name} - HATA: {e}")
        return False

def check_css_content(file_path, css_class, description):
    """CSS dosyasında belirli bir class'ın var olup olmadığını kontrol eder"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if css_class in content:
                print(f"✅ {description}: {css_class} CSS class'ı mevcut")
                return True
            else:
                print(f"❌ {description}: {css_class} CSS class'ı bulunamadı!")
                return False
    except Exception as e:
        print(f"❌ {description}: CSS kontrol hatası - {e}")
        return False

def check_live_feed_integration():
    """Live Feed entegrasyonunu kapsamlı olarak kontrol eder"""
    print("🔍 Live Feed Entegrasyon Kontrolü Başlatılıyor...\n")
    
    all_checks_passed = True
    
    # 1. Dosya varlık kontrolleri
    print("📁 Dosya Varlık Kontrolleri:")
    files_to_check = [
        ("live_feed.py", "Live Feed Ana Modülü"),
        ("ui.py", "UI Ana Modülü"),
        ("download.py", "Download Modülü"),
        ("utils.py", "Utils Modülü"),
        ("stats.py", "Stats Modülü")
    ]
    
    for file_path, description in files_to_check:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    print()
    
    # 2. Import kontrolleri
    print("📦 Import Kontrolleri:")
    imports_to_check = [
        ("live_feed", "Live Feed Modülü"),
        ("ui", "UI Modülü"),
        ("download", "Download Modülü"),
        ("utils", "Utils Modülü"),
        ("stats", "Stats Modülü")
    ]
    
    for module_name, description in imports_to_check:
        if not check_import(module_name, description):
            all_checks_passed = False
    
    print()
    
    # 3. Live Feed fonksiyon kontrolleri
    print("🔧 Live Feed Fonksiyon Kontrolleri:")
    functions_to_check = [
        ("live_feed", "LiveFeedSystem", "LiveFeedSystem Class"),
        ("live_feed", "format_live_feed_html", "HTML Format Fonksiyonu"),
        ("live_feed", "get_platform_logo_base64", "Platform Logo Fonksiyonu"),
        ("live_feed", "live_feed_system", "Global Live Feed Instance")
    ]
    
    for module_name, function_name, description in functions_to_check:
        if not check_function_exists(module_name, function_name, description):
            all_checks_passed = False
    
    print()
    
    # 4. CSS stil kontrolleri
    print("🎨 CSS Stil Kontrolleri:")
    css_classes_to_check = [
        (".live-feed-container", "Ana Live Feed Container"),
        (".feed-header", "Feed Header"),
        (".feed-status-badge", "Status Badge"),
        (".feed-item", "Feed Item"),
        (".progress-bar", "Progress Bar"),
        (".progress-fill", "Progress Fill")
    ]
    
    for css_class, description in css_classes_to_check:
        if not check_css_content("utils.py", css_class, description):
            all_checks_passed = False
    
    print()
    
    # 5. Download entegrasyon kontrolleri
    print("⬇️ Download Entegrasyon Kontrolleri:")
    try:
        with open("download.py", 'r', encoding='utf-8') as f:
            download_content = f.read()
            
        integration_checks = [
            ("live_feed_system.add_download_event", "Download Event Ekleme"),
            ("live_feed_system.update_download_progress", "Progress Güncelleme"),
            ("live_feed_system.complete_download", "Download Tamamlama"),
            ("from live_feed import live_feed_system", "Live Feed Import")
        ]
        
        for check_string, description in integration_checks:
            if check_string in download_content:
                print(f"✅ {description}: Entegre edilmiş")
            else:
                print(f"❌ {description}: Entegre edilmemiş!")
                all_checks_passed = False
                
    except Exception as e:
        print(f"❌ Download.py kontrol hatası: {e}")
        all_checks_passed = False
    
    print()
    
    # 6. UI entegrasyon kontrolleri
    print("🖥️ UI Entegrasyon Kontrolleri:")
    try:
        with open("ui.py", 'r', encoding='utf-8') as f:
            ui_content = f.read()
            
        ui_checks = [
            ("from live_feed import live_feed_system, format_live_feed_html", "Live Feed Import"),
            ("live_feed_data = live_feed_system.get_feed_data()", "Feed Data Alma"),
            ("live_feed_html = format_live_feed_html(live_feed_data)", "HTML Formatı"),
            ("middleDeck.innerHTML = newHtml", "Middle Deck Güncelleme")
        ]
        
        for check_string, description in ui_checks:
            if check_string in ui_content:
                print(f"✅ {description}: Entegre edilmiş")
            else:
                print(f"❌ {description}: Entegre edilmemiş!")
                all_checks_passed = False
                
    except Exception as e:
        print(f"❌ UI.py kontrol hatası: {e}")
        all_checks_passed = False
    
    print()
    
    # 7. Fonksiyonel test
    print("🧪 Fonksiyonel Test:")
    try:
        from live_feed import live_feed_system, format_live_feed_html
        
        # Basit test
        live_feed_system.add_download_event("instagram", "test_user", "https://test.com")
        live_feed_system.update_download_progress("instagram", "test_user", 50)
        
        feed_data = live_feed_system.get_feed_data()
        html_output = format_live_feed_html(feed_data)
        
        if len(html_output) > 100 and "feed-item" in html_output:
            print("✅ Fonksiyonel Test: Başarılı")
        else:
            print("❌ Fonksiyonel Test: Başarısız")
            all_checks_passed = False
            
        # Temizlik
        live_feed_system.complete_download("instagram", "test_user", True)
        
    except Exception as e:
        print(f"❌ Fonksiyonel Test: Hata - {e}")
        all_checks_passed = False
    
    print()
    
    # Sonuç
    if all_checks_passed:
        print("🎉 TÜM KONTROLLER BAŞARILI!")
        print("✅ Live Feed sistemi tamamen entegre edilmiş ve çalışmaya hazır!")
        print("🚀 UI'ı başlatarak middle deck'te live feed'i görebilirsiniz!")
    else:
        print("⚠️ BAZI KONTROLLER BAŞARISIZ!")
        print("❌ Lütfen yukarıdaki hataları düzeltin!")
    
    return all_checks_passed

if __name__ == "__main__":
    check_live_feed_integration() 