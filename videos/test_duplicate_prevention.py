#!/usr/bin/env python3
"""
Test script to verify duplicate download prevention is working correctly.
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from download import normalize_url, get_twitter_id_from_url, download_twitter_media, download_instagram_post_simple

def test_url_normalization():
    """Test URL normalization function"""
    print("Testing URL normalization...")
    
    test_cases = [
        ("https://twitter.com/user/status/1234567890", "https://twitter.com/user/status/1234567890"),
        ("https://x.com/user/status/1234567890", "https://twitter.com/user/status/1234567890"),
        ("https://twitter.com/user/status/1234567890?s=20", "https://twitter.com/user/status/1234567890"),
        ("https://x.com/user/status/1234567890?s=20&t=abc", "https://twitter.com/user/status/1234567890"),
        ("https://instagram.com/p/ABC123/?utm_source=ig", "https://instagram.com/p/ABC123/"),
        ("", ""),
        (None, ""),
    ]
    
    for input_url, expected in test_cases:
        result = normalize_url(input_url)
        status = "✓" if result == expected else "✗"
        print(f"  {status} {input_url} -> {result}")
        if result != expected:
            print(f"    Expected: {expected}")
    
    print()

def test_twitter_id_extraction():
    """Test Twitter ID extraction function"""
    print("Testing Twitter ID extraction...")
    
    test_cases = [
        ("https://twitter.com/user/status/1234567890", "1234567890"),
        ("https://x.com/user/status/1234567890", "1234567890"),
        ("https://twitter.com/user/status/1234567890?s=20", "1234567890"),
        ("https://twitter.com/user", None),
        ("https://instagram.com/p/ABC123/", None),
        ("", None),
    ]
    
    for input_url, expected in test_cases:
        result = get_twitter_id_from_url(input_url)
        status = "✓" if result == expected else "✗"
        print(f"  {status} {input_url} -> {result}")
        if result != expected:
            print(f"    Expected: {expected}")
    
    print()

def test_file_existence_check():
    """Test that download functions check for existing files"""
    print("Testing file existence checking...")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        # Test Twitter file existence check
        twitter_id = "1234567890"
        existing_file = os.path.join(temp_dir, f"{twitter_id}.mp4")
        
        # Create a dummy file
        with open(existing_file, 'w') as f:
            f.write("dummy video content")
        
        print(f"  Created test file: {existing_file}")
        print(f"  File exists: {os.path.exists(existing_file)}")
        
        # Test Instagram shortcode file existence check  
        shortcode = "ABC123"
        existing_instagram_file = os.path.join(temp_dir, f"{shortcode}.mp4")
        
        # Create a dummy Instagram file
        with open(existing_instagram_file, 'w') as f:
            f.write("dummy instagram content")
        
        print(f"  Created test Instagram file: {existing_instagram_file}")
        print(f"  Instagram file exists: {os.path.exists(existing_instagram_file)}")
    
    print()

def test_duplicate_url_detection():
    """Test duplicate URL detection in profile JSON"""
    print("Testing duplicate URL detection...")
    
    # Create test profile data
    profile_data = {
        "username": "test_user",
        "downloaded": [
            {
                "url": "https://twitter.com/user/status/1234567890?s=20",
                "file_path": "/path/to/1234567890.mp4",
                "caption": "test1"
            },
            {
                "url": "https://instagram.com/p/ABC123/?utm_source=ig",
                "file_path": "/path/to/ABC123.mp4", 
                "caption": "test2"
            }
        ]
    }
    
    # Test URLs that should be detected as duplicates
    test_urls = [
        "https://x.com/user/status/1234567890",  # Should match first Twitter URL
        "https://twitter.com/user/status/1234567890?t=xyz",  # Should match first Twitter URL
        "https://instagram.com/p/ABC123/",  # Should match Instagram URL
        "https://twitter.com/user/status/9999999999",  # Should NOT match (different ID)
    ]
    
    # Create normalized downloaded URLs set
    downloaded_urls = {normalize_url(item["url"]) for item in profile_data["downloaded"]}
    print(f"  Downloaded URLs (normalized): {downloaded_urls}")
    
    for test_url in test_urls:
        normalized_test = normalize_url(test_url)
        is_duplicate = normalized_test in downloaded_urls
        expected_duplicate = "1234567890" in test_url or "ABC123" in test_url
        status = "✓" if is_duplicate == expected_duplicate else "✗"
        print(f"  {status} {test_url} -> duplicate: {is_duplicate} (expected: {expected_duplicate})")
    
    print()

def main():
    """Run all tests"""
    print("=== Duplicate Download Prevention Tests ===\n")
    
    test_url_normalization()
    test_twitter_id_extraction()
    test_file_existence_check()
    test_duplicate_url_detection()
    
    print("=== Tests Complete ===")
    print("\nKey improvements implemented:")
    print("1. URL normalization removes query parameters and normalizes domains")
    print("2. Twitter ID extraction for consistent filename generation")
    print("3. File existence checking before download attempts")
    print("4. Normalized URL comparison for duplicate detection")
    print("5. Consistent shortcode-based naming for Instagram files")

if __name__ == "__main__":
    main()
