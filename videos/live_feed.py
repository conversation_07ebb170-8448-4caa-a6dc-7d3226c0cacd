import json
import logging
import os
import threading
import time
import base64
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

PROJECT_ROOT = Path(__file__).resolve().parent

class LiveFeedSystem:
    """
    Canlı akış sistemi - middle deck için gerçek zamanlı indirme durumlarını takip eder.
    """
    
    def __init__(self):
        self.active_downloads = {}  # {unique_id: download_info}
        self.completed_downloads = []  # Son 10 tamamlanan indirme
        self.lock = threading.Lock()
        self.max_completed_items = 15  # Maks<PERSON>um tamamlanmış gösterim sayısı
        
    def add_download_event(self, platform: str, username: str, url: str, 
                          total_count: int = 1, current_index: int = 1):
        """
        Yeni bir indirme eventi ekler.
        """
        unique_id = f"{platform}_{username}_{int(time.time() * 1000)}"
        
        with self.lock:
            self.active_downloads[unique_id] = {
                "id": unique_id,
                "platform": platform,
                "username": username,
                "url": url,
                "total_count": total_count,
                "current_index": current_index,
                "progress": 0,
                "status": "downloading",
                "start_time": datetime.now(),
                "title": self._extract_title_from_url(url)
            }
            
        logging.info(f"Live feed: Yeni indirme eklendi - {platform}/{username}")
        
    def update_download_progress(self, platform: str, username: str, progress: int):
        """
        Mevcut bir indirmenin ilerlemesini günceller.
        """
        with self.lock:
            for download_id, info in self.active_downloads.items():
                if info["platform"] == platform and info["username"] == username:
                    info["progress"] = min(100, max(0, progress))
                    break
                    
    def complete_download(self, platform: str, username: str, success: bool = True):
        """
        Bir indirmeyi tamamlanmış olarak işaretler.
        """
        with self.lock:
            completed_item = None
            for download_id, info in list(self.active_downloads.items()):
                if info["platform"] == platform and info["username"] == username:
                    info["status"] = "completed" if success else "failed"
                    info["end_time"] = datetime.now()
                    completed_item = info.copy()
                    del self.active_downloads[download_id]
                    break
                    
            if completed_item:
                self.completed_downloads.insert(0, completed_item)
                # Maksimum sayıyı aş mı?
                if len(self.completed_downloads) > self.max_completed_items:
                    self.completed_downloads = self.completed_downloads[:self.max_completed_items]
                    
                logging.info(f"Live feed: İndirme tamamlandı - {platform}/{username}")
                
    def get_feed_data(self) -> Dict:
        """
        UI için feed verilerini döndürür.
        """
        with self.lock:
            return {
                "active_downloads": list(self.active_downloads.values()),
                "completed_downloads": self.completed_downloads.copy(),
                "has_any_activity": len(self.active_downloads) > 0 or len(self.completed_downloads) > 0
            }
            
    def _extract_title_from_url(self, url: str) -> str:
        """
        URL'den basit bir başlık çıkarır.
        """
        try:
            if "instagram.com" in url:
                # Instagram shortcode çıkar
                parts = url.split("/")
                for i, part in enumerate(parts):
                    if part in ["p", "reel", "tv"] and i + 1 < len(parts):
                        return f"Post: {parts[i + 1][:8]}..."
            elif "twitter.com" in url or "x.com" in url:
                # Twitter status ID çıkar
                if "/status/" in url:
                    status_id = url.split("/status/")[1].split("?")[0]
                    return f"Tweet: {status_id[-8:]}..."
            elif "youtube.com" in url or "youtu.be" in url:
                return "YouTube Video"
                
            return "Media Content"
        except Exception:
            return "Content"
            
    def clear_old_completed(self, hours: int = 24):
        """
        Eski tamamlanmış indirmeleri temizler.
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        with self.lock:
            self.completed_downloads = [
                item for item in self.completed_downloads 
                if item.get("end_time", datetime.now()) > cutoff_time
            ]


def get_platform_logo_base64(platform: str) -> str:
    """
    Platform logosunu base64 formatında döndürür.
    """
    try:
        logo_file = f"{platform.lower()}.png"
        logo_path = PROJECT_ROOT / logo_file
        
        if logo_path.exists():
            with open(logo_path, "rb") as img_f:
                b64_logo = base64.b64encode(img_f.read()).decode()
            return f'<img src="data:image/png;base64,{b64_logo}" style="height:24px; width:auto; vertical-align:middle; margin-right:8px;" />'
    except Exception as e:
        logging.debug(f"Logo yüklenemedi {platform}: {e}")
    
    # Fallback icon
    if platform.lower() == "instagram":
        return '<div style="width:24px; height:24px; margin-right:8px; background:#E4405F; border-radius:6px; display:inline-block; vertical-align:middle;"></div>'
    elif platform.lower() == "twitter":
        return '<div style="width:24px; height:24px; margin-right:8px; background:#1DA1F2; border-radius:6px; display:inline-block; vertical-align:middle;"></div>'
    else:
        return '<div style="width:24px; height:24px; margin-right:8px; background:#666; border-radius:6px; display:inline-block; vertical-align:middle;"></div>'


def format_live_feed_html(feed_data: Dict) -> str:
    """
    Live feed verisini HTML formatına çevirir.
    """
    active_downloads = feed_data.get("active_downloads", [])
    completed_downloads = feed_data.get("completed_downloads", [])
    has_any_activity = feed_data.get("has_any_activity", False)
    
    if not has_any_activity:
        # Skeleton animasyon göster
        return '''
        <div class="live-feed-container">
            <div class="feed-header">
                <h3>Canlı Akış</h3>
                <div class="feed-status-badge idle">Beklemede</div>
            </div>
            <div class="skeleton-container" id="middle-skeleton">
                <div class="skeleton" style="width: 80%; height: 24px;"></div>
                <div class="skeleton" style="width: 60%; height: 20px;"></div>
                <div class="skeleton" style="width: 90%; height: 24px;"></div>
                <div class="skeleton" style="width: 70%; height: 18px;"></div>
            </div>
        </div>
        '''
    
    html = '''
    <div class="live-feed-container">
        <div class="feed-header">
            <h3>Canlı Akış</h3>
            <div class="feed-status-badge active">Aktif</div>
        </div>
        <div class="feed-content">
    '''
    
    # Aktif indirmeler
    if active_downloads:
        html += '<div class="feed-section"><h4>İndiriliyor</h4>'
        for download in active_downloads:
            platform_logo = get_platform_logo_base64(download["platform"])
            progress = download.get("progress", 0)
            
            counter_text = ""
            if download.get("total_count", 1) > 1:
                counter_text = f'<span class="download-counter">{download.get("current_index", 1)}/{download.get("total_count", 1)}</span>'
            
            html += f'''
            <div class="feed-item active-download" data-id="{download["id"]}">
                <div class="feed-item-header">
                    {platform_logo}
                    <div class="feed-item-info">
                        <div class="feed-username">@{download["username"]}</div>
                        <div class="feed-title">{download["title"]}</div>
                    </div>
                    {counter_text}
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {progress}%"></div>
                    </div>
                    <div class="progress-text">{progress}%</div>
                </div>
            </div>
            '''
        html += '</div>'
    
    # Tamamlanan indirmeler
    if completed_downloads:
        html += '<div class="feed-section"><h4>Tamamlanan</h4>'
        for download in completed_downloads[:8]:  # Son 8 tane göster
            platform_logo = get_platform_logo_base64(download["platform"])
            status_icon = "✓" if download.get("status") == "completed" else "✗"
            status_class = "success" if download.get("status") == "completed" else "failed"
            
            # Süre hesapla
            try:
                start_time = download.get("start_time")
                end_time = download.get("end_time")
                if isinstance(start_time, str):
                    start_time = datetime.fromisoformat(start_time)
                if isinstance(end_time, str):
                    end_time = datetime.fromisoformat(end_time)
                
                if start_time and end_time:
                    duration = (end_time - start_time).total_seconds()
                    duration_text = f"{duration:.1f}s"
                else:
                    duration_text = "N/A"
            except Exception:
                duration_text = "N/A"
            
            html += f'''
            <div class="feed-item completed-download {status_class}">
                <div class="feed-item-header">
                    {platform_logo}
                    <div class="feed-item-info">
                        <div class="feed-username">@{download["username"]}</div>
                        <div class="feed-title">{download["title"]}</div>
                    </div>
                    <div class="completion-info">
                        <span class="status-icon">{status_icon}</span>
                        <span class="duration">{duration_text}</span>
                    </div>
                </div>
            </div>
            '''
        html += '</div>'
    
    html += '''
        </div>
    </div>
    '''
    
    return html


# Global live feed sistemi
live_feed_system = LiveFeedSystem() 