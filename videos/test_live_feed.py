#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Live Feed System Test
Bu dosya live feed sisteminin çalışıp çalışmadığını test eder.
"""

import time
import json
from live_feed import live_feed_system, format_live_feed_html

def test_live_feed():
    print("🔍 Live Feed Sistemi Test Ediliyor...")
    
    # 1. Boş durumda test
    print("\n1. Boş durum testi:")
    empty_data = live_feed_system.get_feed_data()
    empty_html = format_live_feed_html(empty_data)
    print(f"   ✓ Boş HTML uzunluğu: {len(empty_html)} karakter")
    print(f"   ✓ Skeleton içeriyor mu: {'skeleton-item' in empty_html}")
    
    # 2. Instagram indirme simülasyonu
    print("\n2. Instagram indirme simülasyonu:")
    live_feed_system.add_download_event("instagram", "test_user", "https://instagram.com/p/ABC123/")
    
    # <PERSON><PERSON><PERSON><PERSON>
    test_url = "https://instagram.com/p/ABC123/"
    
    print("   ✓ %10 ilerleme...")
    live_feed_system.update_download_progress("instagram", "test_user", 10)
    time.sleep(0.5)
    
    print("   ✓ %30 ilerleme...")
    live_feed_system.update_download_progress("instagram", "test_user", 30)
    time.sleep(0.5)
    
    print("   ✓ %50 ilerleme...")
    live_feed_system.update_download_progress("instagram", "test_user", 50)
    time.sleep(0.5)
    
    print("   ✓ %80 ilerleme...")
    live_feed_system.update_download_progress("instagram", "test_user", 80)
    time.sleep(0.5)
    
    print("   ✓ %100 ilerleme...")
    live_feed_system.update_download_progress("instagram", "test_user", 100)
    live_feed_system.complete_download("instagram", "test_user", True)
    
    # 3. Twitter indirme simülasyonu
    print("\n3. Twitter indirme simülasyonu:")
    twitter_url = "https://twitter.com/user/status/456789"
    live_feed_system.add_download_event("twitter", "twitter_user", twitter_url)
    
    print("   ✓ %25 ilerleme...")
    live_feed_system.update_download_progress("twitter", "twitter_user", 25)
    time.sleep(0.5)
    
    print("   ✓ %75 ilerleme...")
    live_feed_system.update_download_progress("twitter", "twitter_user", 75)
    time.sleep(0.5)
    
    print("   ✓ %100 ilerleme...")
    live_feed_system.update_download_progress("twitter", "twitter_user", 100)
    live_feed_system.complete_download("twitter", "twitter_user", True)
    
    # 4. Aktif durumda HTML çıktısı
    print("\n4. Aktif durum HTML testi:")
    active_data = live_feed_system.get_feed_data()
    active_html = format_live_feed_html(active_data)
    print(f"   ✓ Aktif HTML uzunluğu: {len(active_html)} karakter")
    print(f"   ✓ Feed item içeriyor mu: {'feed-item' in active_html}")
    print(f"   ✓ İkinci öğe var mı: {active_html.count('feed-item') >= 2}")
    
    # 5. Feed verilerini görüntüle
    print("\n5. Live Feed verileri:")
    feed_data = live_feed_system.get_feed_data()
    print(f"   ✓ Toplam aktif event: {len(feed_data.get('active_downloads', []))}")
    print(f"   ✓ Toplam tamamlanan: {len(feed_data.get('completed_downloads', []))}")
    
    all_events = feed_data.get('active_downloads', []) + feed_data.get('completed_downloads', [])
    for i, event in enumerate(all_events, 1):
        platform = event.get('platform', 'Unknown')
        username = event.get('username', 'Unknown')
        progress = event.get('progress', 100)  # Tamamlananlar için 100
        status = event.get('status', 'active')
        print(f"   {i}. {platform.title()} | @{username} | %{progress} | {status}")
    
    print("\n✅ Live Feed sistemi başarıyla test edildi!")
    print("🚀 UI'da middle deck'te live feed görünmelidir!")

if __name__ == "__main__":
    test_live_feed() 