#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Live Feed sistemini test etmek için basit test scripti
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Live Feed manager'ı import et
from live_feed import live_feed_manager
from utils import get_logo_path

def test_live_feed():
    """Live Feed sistemini test eder"""
    print("Live Feed sistemi test ediliyor...")
    
    # Test event'leri olu<PERSON>tur
    test_events = [
        {
            "type": "download_start",
            "platform": "instagram",
            "username": "test_user",
            "logo_path": get_logo_path("instagram"),
            "progress": 0,
            "current": 1,
            "total": 3,
            "status": "start"
        },
        {
            "type": "download_progress",
            "platform": "instagram", 
            "username": "test_user",
            "logo_path": get_logo_path("instagram"),
            "progress": 50,
            "current": 2,
            "total": 3,
            "status": "progress"
        },
        {
            "type": "download_complete",
            "platform": "instagram",
            "username": "test_user", 
            "logo_path": get_logo_path("instagram"),
            "progress": 100,
            "current": 3,
            "total": 3,
            "status": "complete"
        },
        {
            "type": "upload_post",
            "platform": "twitter",
            "username": "test_twitter",
            "logo_path": get_logo_path("twitter"),
            "desc": "Test tweet açıklaması burada...",
            "thumb": get_logo_path("twitter")  # Test için logo'yu thumbnail olarak kullan
        }
    ]
    
    # Event'leri sırayla ekle
    for i, event in enumerate(test_events):
        print(f"Event {i+1} ekleniyor: {event['type']}")
        live_feed_manager.add_event(event)
        time.sleep(2)  # 2 saniye bekle
    
    print("Test tamamlandı!")
    print(f"Toplam event sayısı: {len(live_feed_manager.get_events())}")

if __name__ == "__main__":
    # QApplication oluştur (PyQt sinyalleri için gerekli)
    app = QApplication(sys.argv)
    
    # Test'i çalıştır
    test_live_feed()
    
    # 10 saniye bekle
    QTimer.singleShot(10000, app.quit)
    app.exec_() 