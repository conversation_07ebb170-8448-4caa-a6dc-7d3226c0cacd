#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Ana uygulamaya manuel Live Feed event'i gönderen test scripti
"""

from signals import feed_emitter
from utils import get_logo_path

def send_test_event():
    """Test event'i ana uygulamaya gönderir"""
    print("Ana uygulamaya test event'i gönderiliyor...")
    
    # Test download event'i
    test_event = {
        "type": "download_progress",
        "platform": "instagram",
        "username": "manuel_test",
        "logo_path": get_logo_path("instagram"),
        "progress": 75,
        "current": 2,
        "total": 3,
        "status": "progress"
    }
    
    # Signal ile event'i gönder
    feed_emitter.add_item_to_feed_signal.emit(test_event)
    print("Test event'i gönderildi!")

if __name__ == "__main__":
    send_test_event() 