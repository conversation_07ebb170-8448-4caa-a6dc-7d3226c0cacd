{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "tr-TR"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21586, "default_apps_install_state": 2, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "********-55d8-4b4e-9679-b7fafec95a4e", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.334608, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "87c32206-f5e9-4271-a5a3-405a959f7df4"}}, "intl": {"accept_languages": "tr-TR,tr,en-US,en", "selected_languages": "tr-TR,tr,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "tMI3I7Mm3mnOMVyVNqFuU9OgJjdOBL2p1teaBZexa+kBre/gH3WIeUjFSom4GHHYfkZlWPX8ULLnK+a3v3FcLw=="}, "ntp": {"num_personal_suggestions": 4}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://x.com:443,*": {"last_modified": "*****************", "setting": {"https://x.com/": {"next_install_text_animation": {"delay": "***********", "last_shown": "*****************"}}, "https://x.com/?utm_source=homescreen&utm_medium=shortcut": {"couldShowBannerEvents": 1.3392863934095346e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]x.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13392864480554388", "setting": {"lastEngagementTime": 1.3392864480554368e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.5, "rawScore": 7.5}}, "https://x.com:443,*": {"last_modified": "13392864505475440", "setting": {"lastEngagementTime": 1.3392864505475426e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 9.6, "rawScore": 9.6}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "creation_time": "13392784622443108", "default_content_setting_values": {"notifications": 2}, "default_content_settings": {"popups": 0}, "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Crashed", "last_engagement_time": "13392864505475426", "last_time_obsolete_http_credentials_removed": 1748390940.378189, "last_time_password_store_metrics_reported": 1748390910.377692, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_default_content_settings": {"images": 2}, "managed_user_id": "", "name": "Chrome'unuz", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13393123132412903", "hash_real_time_ohttp_key": "0gAgz7NnQTpxaNrkE/W7d+fc3Njc9M9rqIL5N2yollHsMw4ABAABAAI=", "metrics_last_log_time": "13392863931", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQkKai0qqV5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEPCI/ovSl+UX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13392777599000000", "uma_in_sql_start_time": "13392863931980415"}, "sessions": {"event_log": [{"crashed": false, "time": "13392863931976226", "type": 0}, {"crashed": false, "time": "13392864204064794", "type": 0}, {"crashed": false, "time": "13392864315304750", "type": 0}, {"crashed": false, "time": "13392864480374636", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["tr"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "updateclientdata": {"apps": {"nmmhkkegccagdldgiimedpiccmgmieda": {"cohort": "1::", "cohortname": "", "dlrc": 6721, "installdate": 6721, "pf": "b7e0fc0a-486f-4331-9079-02cb71423eb4"}}}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"kuma 18 bölüm\",\"av<PERSON><PERSON><PERSON> g<PERSON><PERSON>\",\"burhan can terzi morata\",\"merkez bankası faiz kararı\",\"tcdd tren makinisti kursu\",\"cristiano ronaldo transfer\",\"iphone 17 pro\",\"prens\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChwIkk4SFwoTVHJlbmQgb2xhbiBhcmFtYWxhcigK\",\"google:suggestdetail\":[{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\\u003d\",\"zl\":10002}],\"google:suggesteventid\":\"6607850711402773739\",\"google:suggestrelevance\":[1256,1255,1254,1253,1252,1251,1250,600],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\"]}]"}}