�f�5            �f�5            �f�5            U=Ò           
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10S�T�X          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther (�ٖ�10��L�           
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�P�J�           
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10M�I`           
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�ޮ�          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10����6          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10��Ja          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�!�AW	          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10D�IG� 
          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10]�#d1           	39_config
��؈��O�ԓ �ٖ�1P�=I~           	39_configf
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1��B(� 
          	39_config�
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1
�ހ���`�ԓ �ٖ�1
"�������d�ԓ �ٖ�1(���ʖ����s~gA�           	39_config�
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1
�ހ���`�ԓ �ٖ�1
"�������d�ԓ �ٖ�1(���ʖ����
ۯ��Њ���ԓ �ٖ�1	H��;          	39_config�
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1
�ހ���`�ԓ �ٖ�1
"�������d�ԓ �ٖ�1(���ʖ����
ۯ��Њ���ԓ �ٖ�1
��՛�����ԓ �ٖ�1
���Åօ�C�ԓ �ٖ�1
�����Ӆ���ԓ �ٖ�1
��������_�ԓ �ٖ�1��-�          	39_config�
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1
�ހ���`�ԓ �ٖ�1
"�������d�ԓ �ٖ�1(���ʖ����
ۯ��Њ���ԓ �ٖ�1
��՛�����ԓ �ٖ�1
���Åօ�C�ԓ �ٖ�1
�����Ӆ���ԓ �ٖ�1
��������_�ԓ �ٖ�1
�����������I �ٖ�1
�������I �ٖ�1
ʒ���қ�C��I �ٖ�1
���޾���,��I �ٖ�1
���������I �ٖ�1emP          	39_config�
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1
�ހ���`�ԓ �ٖ�1
"�������d�ԓ �ٖ�1(���ʖ����
ۯ��Њ���ԓ �ٖ�1
��՛�����ԓ �ٖ�1
���Åօ�C�ԓ �ٖ�1
�����Ӆ���ԓ �ٖ�1
��������_�ԓ �ٖ�1
�����������I �ٖ�1
�������I �ٖ�1
ʒ���қ�C��I �ٖ�1
���޾���,��I �ٖ�1
���������I �ٖ�1
������t�ԓ �ٖ�1
��������k�ԓ �ٖ�1
գ��������ԓ �ٖ�1
��ר�ٳ���ԓ �ٖ�1
ෛ�������ԓ �ٖ�1
������Ʉ��ԓ �ٖ�1�{v�          	39_config�
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1
�ހ���`�ԓ �ٖ�1
"�������d�ԓ �ٖ�1(���ʖ����
ۯ��Њ���ԓ �ٖ�1
��՛�����ԓ �ٖ�1
���Åօ�C�ԓ �ٖ�1
�����Ӆ���ԓ �ٖ�1
��������_�ԓ �ٖ�1
�����������I �ٖ�1
�������I �ٖ�1
ʒ���қ�C��I �ٖ�1
���޾���,��I �ٖ�1
���������I �ٖ�1
������t�ԓ �ٖ�1
��������k�ԓ �ٖ�1
գ��������ԓ �ٖ�1
��ר�ٳ���ԓ �ٖ�1
ෛ�������ԓ �ٖ�1
������Ʉ��ԓ �ٖ�1
"��ї�Z�ԓ �ٖ�1(Ȏ�������
#�򖐩�����ԓ �ٖ�1(Ȏ�������
#�ɕԺ����ԓ �ٖ�1(Ȏ�������g	�K�          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10G��4           20_1_1
1��}x;       -   4_EsbDownloadRowPromo
EsbDownloadRowPromo��4_IPH_BatterySaverMode
IPH_BatterySaverMode��4_IPH_CompanionSidePanel
IPH_CompanionSidePanel��$4_IPH_CompanionSidePanelRegionSearch(
"IPH_CompanionSidePanelRegionSearch��4_IPH_DesktopCustomizeChrome 
IPH_DesktopCustomizeChrome��4_IPH_DiscardRing
IPH_DiscardRing��4_IPH_DownloadEsbPromo
IPH_DownloadEsbPromo��/4_IPH_ExplicitBrowserSigninPreferenceRemembered3
-IPH_ExplicitBrowserSigninPreferenceRemembered��4_IPH_HistorySearch
IPH_HistorySearch��&4_IPH_FocusHelpBubbleScreenReaderPromo*
$IPH_FocusHelpBubbleScreenReaderPromo��4_IPH_GMCCastStartStop
IPH_GMCCastStartStop��4_IPH_GMCLocalMediaCasting
IPH_GMCLocalMediaCasting��4_IPH_HighEfficiencyMode
IPH_HighEfficiencyMode��4_IPH_LiveCaption
IPH_LiveCaption��(4_IPH_PasswordsManagementBubbleAfterSave,
&IPH_PasswordsManagementBubbleAfterSave��+4_IPH_PasswordsManagementBubbleDuringSignin/
)IPH_PasswordsManagementBubbleDuringSignin��"4_IPH_PasswordsWebAppProfileSwitch&
 IPH_PasswordsWebAppProfileSwitch��4_IPH_PasswordSharingFeature 
IPH_PasswordSharingFeature��4_IPH_PdfSearchifyFeature
IPH_PdfSearchifyFeature��*4_IPH_PerformanceInterventionDialogFeature.
(IPH_PerformanceInterventionDialogFeature��-4_IPH_PriceInsightsPageActionIconLabelFeature1
+IPH_PriceInsightsPageActionIconLabelFeature��&4_IPH_PriceTrackingEmailConsentFeature*
$IPH_PriceTrackingEmailConsentFeature��-4_IPH_PriceTrackingPageActionIconLabelFeature1
+IPH_PriceTrackingPageActionIconLabelFeature��4_IPH_ReadingModeSidePanel
IPH_ReadingModeSidePanel��4_IPH_ShoppingCollectionFeature#
IPH_ShoppingCollectionFeature��%4_IPH_SidePanelGenericPinnableFeature)
#IPH_SidePanelGenericPinnableFeature��)4_IPH_SidePanelLensOverlayPinnableFeature-
'IPH_SidePanelLensOverlayPinnableFeature��14_IPH_SidePanelLensOverlayPinnableFollowupFeature5
/IPH_SidePanelLensOverlayPinnableFollowupFeature��4_IPH_SignoutWebIntercept
IPH_SignoutWebIntercept��4_IPH_TabGroupsSaveV2Intro
IPH_TabGroupsSaveV2Intro��4_IPH_TabGroupsSaveV2CloseGroup#
IPH_TabGroupsSaveV2CloseGroup��4_IPH_ProfileSwitch
IPH_ProfileSwitch��4_IPH_PriceTrackingInSidePanel"
IPH_PriceTrackingInSidePanel��4_IPH_PwaQuietNotification
IPH_PwaQuietNotification��4_IPH_AutofillAiOptIn
IPH_AutofillAiOptIn��.4_IPH_AutofillExternalAccountProfileSuggestion2
,IPH_AutofillExternalAccountProfileSuggestion��&4_IPH_AutofillVirtualCardCVCSuggestion*
$IPH_AutofillVirtualCardCVCSuggestion��#4_IPH_AutofillVirtualCardSuggestion'
!IPH_AutofillVirtualCardSuggestion��4_IPH_CookieControls
IPH_CookieControls��$4_IPH_DesktopPWAsLinkCapturingLaunch(
"IPH_DesktopPWAsLinkCapturingLaunch��,4_IPH_DesktopPWAsLinkCapturingLaunchAppInTab0
*IPH_DesktopPWAsLinkCapturingLaunchAppInTab��!4_IPH_SupervisedUserProfileSignin%
IPH_SupervisedUserProfileSignin��4_IPH_iOSPasswordPromoDesktop!
IPH_iOSPasswordPromoDesktop��4_IPH_iOSAddressPromoDesktop 
IPH_iOSAddressPromoDesktop��4_IPH_iOSPaymentPromoDesktop 
IPH_iOSPaymentPromoDesktop��r �M B           ����CB          &9_963f0295-8e31-4148-9a5d-e89706d064c9�	$963f0295-8e31-4148-9a5d-e89706d064c9��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\963f0295-8e31-4148-9a5d-e89706d064c98���Ԫ��@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION���yfC          &9_1253e4dd-687b-445a-8ee0-a65a035ff3e7�	$1253e4dd-687b-445a-8ee0-a65a035ff3e7��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1253e4dd-687b-445a-8ee0-a65a035ff3e78���Ԫ��@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS.\=D          &9_6b655e64-d470-4522-a78f-7e1cd2703714�	$6b655e64-d470-4522-a78f-7e1cd2703714��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6b655e64-d470-4522-a78f-7e1cd27037148���Ԫ��@ H Pϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY<�s�2E          &9_d25ee089-d7f4-4a10-a055-8b34b296a8e1�	$d25ee089-d7f4-4a10-a055-8b34b296a8e1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\d25ee089-d7f4-4a10-a055-8b34b296a8e18��Ԫ��@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2?�qdF          &9_0c543083-fc44-48da-8d45-249cc51d6baa�	$0c543083-fc44-48da-8d45-249cc51d6baa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0c543083-fc44-48da-8d45-249cc51d6baa8���Ԫ��@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS.��(GG          &9_1ece3d62-c815-4f05-b86d-010a2b4a19a9�	$1ece3d62-c815-4f05-b86d-010a2b4a19a9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1ece3d62-c815-4f05-b86d-010a2b4a19a98���Ԫ��@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�G�EH          &9_7c6b7370-5641-4756-99a6-30505544c92b�	$7c6b7370-5641-4756-99a6-30505544c92b��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\7c6b7370-5641-4756-99a6-30505544c92b8Ţ�Ԫ��@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGa^��^I          &9_ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�	$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\ad42e475-75a7-48cb-84ba-00b2cb9cb5fd8��Ԫ��@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION�N>)CJ          &9_963f0295-8e31-4148-9a5d-e89706d064c9�	$963f0295-8e31-4148-9a5d-e89706d064c9��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\963f0295-8e31-4148-9a5d-e89706d064c98���Ԫ��@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION�u��CK          &9_963f0295-8e31-4148-9a5d-e89706d064c9�	$963f0295-8e31-4148-9a5d-e89706d064c9��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\963f0295-8e31-4148-9a5d-e89706d064c98���Ԫ��@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION��SCL          &9_963f0295-8e31-4148-9a5d-e89706d064c9�	$963f0295-8e31-4148-9a5d-e89706d064c9��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\963f0295-8e31-4148-9a5d-e89706d064c98���Ԫ��@ HPϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTIONX޺fM          &9_1253e4dd-687b-445a-8ee0-a65a035ff3e7�	$1253e4dd-687b-445a-8ee0-a65a035ff3e7��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1253e4dd-687b-445a-8ee0-a65a035ff3e78���Ԫ��@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSk���fN          &9_1253e4dd-687b-445a-8ee0-a65a035ff3e7�	$1253e4dd-687b-445a-8ee0-a65a035ff3e7��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1253e4dd-687b-445a-8ee0-a65a035ff3e78���Ԫ��@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�u�fO          &9_1253e4dd-687b-445a-8ee0-a65a035ff3e7�	$1253e4dd-687b-445a-8ee0-a65a035ff3e7��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1253e4dd-687b-445a-8ee0-a65a035ff3e78���Ԫ��@ HPϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSR}�n=P          &9_6b655e64-d470-4522-a78f-7e1cd2703714�	$6b655e64-d470-4522-a78f-7e1cd2703714��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6b655e64-d470-4522-a78f-7e1cd27037148���Ԫ��@ H Pϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY�&O�2Q          &9_d25ee089-d7f4-4a10-a055-8b34b296a8e1�	$d25ee089-d7f4-4a10-a055-8b34b296a8e1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\d25ee089-d7f4-4a10-a055-8b34b296a8e18��Ԫ��@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2�.ɭdR          &9_0c543083-fc44-48da-8d45-249cc51d6baa�	$0c543083-fc44-48da-8d45-249cc51d6baa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0c543083-fc44-48da-8d45-249cc51d6baa8���Ԫ��@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS(�^GS          &9_1ece3d62-c815-4f05-b86d-010a2b4a19a9�	$1ece3d62-c815-4f05-b86d-010a2b4a19a9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1ece3d62-c815-4f05-b86d-010a2b4a19a98���Ԫ��@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGK�[�ET          &9_7c6b7370-5641-4756-99a6-30505544c92b�	$7c6b7370-5641-4756-99a6-30505544c92b��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\7c6b7370-5641-4756-99a6-30505544c92b8Ţ�Ԫ��@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING	<��^U          &9_ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�	$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\ad42e475-75a7-48cb-84ba-00b2cb9cb5fd8��Ԫ��@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION|5�V          021_download,963f0295-8e31-4148-9a5d-e89706d064c9�
�
$963f0295-8e31-4148-9a5d-e89706d064c9
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj       r       x ��خ������������� �� � � � � � � ����������������������


     �@�aW          &9_963f0295-8e31-4148-9a5d-e89706d064c9�	$963f0295-8e31-4148-9a5d-e89706d064c9��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\963f0295-8e31-4148-9a5d-e89706d064c98���Ԫ��@ HPϟ�/X ` p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:AAO2VwqmW61bQmXeQLId1vAjbZ7kZdp5FAu0qOT_cZDd2YOlWAR156RNRQaiBWgutIgc5X_8 accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:12 GMT expires:Wed, 28 May 2025 01:57:12 GMT x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION�
�X          021_download,963f0295-8e31-4148-9a5d-e89706d064c9�
�
$963f0295-8e31-4148-9a5d-e89706d064c9
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   1 8 3 7 1 7 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 6 3 f 0 2 9 5 - 8 e 3 1 - 4 1 4 8 - 9 a 5 d - e 8 9 7 0 6 d 0 6 4 c 9   x ��خ������������� �� �� � � � � ����������������������


     L��Y          021_download,1253e4dd-687b-445a-8ee0-a65a035ff3e7�
�
$1253e4dd-687b-445a-8ee0-a65a035ff3e7
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj       r       x ��ٮ������������� �� � � � � � � ����������������������


     nU�ǔZ          &9_1253e4dd-687b-445a-8ee0-a65a035ff3e7�	$1253e4dd-687b-445a-8ee0-a65a035ff3e7��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1253e4dd-687b-445a-8ee0-a65a035ff3e78���Ԫ��@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:AAO2VwoQ8q9OtgLYVt83CAJu27Ci794L9mX-sG0bEDmqKy1nj0HTYIXCOgWKz4Up-cJzms8N cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:13 GMT expires:Wed, 28 May 2025 01:57:13 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=fqAwWA== content-length:5156 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSZa&�A[          021_download,1253e4dd-687b-445a-8ee0-a65a035ff3e7�
�
$1253e4dd-687b-445a-8ee0-a65a035ff3e7
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   9 0 2 9 7 2 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 2 5 3 e 4 d d - 6 8 7 b - 4 4 5 a - 8 e e 0 - a 6 5 a 0 3 5 f f 3 e 7   x�(��ٮ������������� �?����_6��:�g��}b���vO��{n7��� �� � � � � ����������������������


     ��o\          021_download,1253e4dd-687b-445a-8ee0-a65a035ff3e7�
�
$1253e4dd-687b-445a-8ee0-a65a035ff3e7
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 2 5 3 e 4 d d - 6 8 7 b - 4 4 5 a - 8 e e 0 - a 6 5 a 0 3 5 f f 3 e 7   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 2 5 3 e 4 d d - 6 8 7 b - 4 4 5 a - 8 e e 0 - a 6 5 a 0 3 5 f f 3 e 7   x�(��ٮ����ۮ��� �?����_6��:�g��}b���vO��{n7����� � � � � ����������������������


     021_download,963f0295-8e31-4148-9a5d-e89706d064c9�
�
$963f0295-8e31-4148-9a5d-e89706d064c9
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   1 8 3 7 1 7 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 6 3 f 0 2 9 5 - 8 e 3 1 - 4 1 4 8 - 9 a 5 d - e 8 9 7 0 6 d 0 6 4 c 9   x���خ������������� �!��Q�k���֧*��:{`B��aOW�+V�@�� �� � � � � ����������������������


     fU��^          &9_1253e4dd-687b-445a-8ee0-a65a035ff3e7�	$1253e4dd-687b-445a-8ee0-a65a035ff3e7��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1253e4dd-687b-445a-8ee0-a65a035ff3e78���Ԫ��@���Ԫ��HPϟ�/X�(`���Ԫ��p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:AAO2VwoQ8q9OtgLYVt83CAJu27Ci794L9mX-sG0bEDmqKy1nj0HTYIXCOgWKz4Up-cJzms8N cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:13 GMT expires:Wed, 28 May 2025 01:57:13 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=fqAwWA== content-length:5156 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��[�=_          &9_6b655e64-d470-4522-a78f-7e1cd2703714�	$6b655e64-d470-4522-a78f-7e1cd2703714��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6b655e64-d470-4522-a78f-7e1cd27037148���Ԫ��@ H Pϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY�$MN=`          &9_6b655e64-d470-4522-a78f-7e1cd2703714�	$6b655e64-d470-4522-a78f-7e1cd2703714��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6b655e64-d470-4522-a78f-7e1cd27037148���Ԫ��@ HPϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY(�)�a          021_download,1253e4dd-687b-445a-8ee0-a65a035ff3e7�
�
$1253e4dd-687b-445a-8ee0-a65a035ff3e7
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852548&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 2 5 3 e 4 d d - 6 8 7 b - 4 4 5 a - 8 e e 0 - a 6 5 a 0 3 5 f f 3 e 7   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e �Fq:s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 2 5 3 e 4 d d - 6 8 7 b - 4 4 5 a - 8 e e 0 - a 6 5 a 0 3 5 f f 3 e 7   x�(��ٮ����ۮ��� �?����_6��:�g��}b���vO��{n7����� � � � � ����������������������


     
�> b           021_download,1253e4dd-687b-445a-8ee0-a65a035ff3e75�q�:c          021_download,963f0295-8e31-4148-9a5d-e89706d064c9�
�
$963f0295-8e31-4148-9a5d-e89706d064c9
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 6 3 f 0 2 9 5 - 8 e 3 1 - 4 1 4 8 - 9 a 5 d - e 8 9 7 0 6 d 0 6 4 c 9   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 6 3 f 0 2 9 5 - 8 e 3 1 - 4 1 4 8 - 9 a 5 d - e 8 9 7 0 6 d 0 6 4 c 9   x���خ����ۮ��� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     g���qd          &9_963f0295-8e31-4148-9a5d-e89706d064c9�	$963f0295-8e31-4148-9a5d-e89706d064c9��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\963f0295-8e31-4148-9a5d-e89706d064c98���Ԫ��@���Ԫ��HPϟ�/X�`���Ԫ��p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:AAO2VwqmW61bQmXeQLId1vAjbZ7kZdp5FAu0qOT_cZDd2YOlWAR156RNRQaiBWgutIgc5X_8 accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:12 GMT expires:Wed, 28 May 2025 01:57:12 GMT x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION!�_g2e          &9_d25ee089-d7f4-4a10-a055-8b34b296a8e1�	$d25ee089-d7f4-4a10-a055-8b34b296a8e1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\d25ee089-d7f4-4a10-a055-8b34b296a8e18��Ԫ��@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2v��2f          &9_d25ee089-d7f4-4a10-a055-8b34b296a8e1�	$d25ee089-d7f4-4a10-a055-8b34b296a8e1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\d25ee089-d7f4-4a10-a055-8b34b296a8e18��Ԫ��@ HPϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2>��s:g          021_download,963f0295-8e31-4148-9a5d-e89706d064c9�
�
$963f0295-8e31-4148-9a5d-e89706d064c9
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 6 3 f 0 2 9 5 - 8 e 3 1 - 4 1 4 8 - 9 a 5 d - e 8 9 7 0 6 d 0 6 4 c 9   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 6 3 f 0 2 9 5 - 8 e 3 1 - 4 1 4 8 - 9 a 5 d - e 8 9 7 0 6 d 0 6 4 c 9   x���خ����ۮ��� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     -> h           021_download,963f0295-8e31-4148-9a5d-e89706d064c9L}U��i          021_download,6b655e64-d470-4522-a78f-7e1cd2703714�
�
$6b655e64-d470-4522-a78f-7e1cd2703714
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj       r       x ��ۮ������������� �� � � � � � � ����������������������


     j�OfXj          &9_6b655e64-d470-4522-a78f-7e1cd2703714�	$6b655e64-d470-4522-a78f-7e1cd2703714��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6b655e64-d470-4522-a78f-7e1cd27037148���Ԫ��@ HPϟ�/X ` p x �phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY��HTTP/1.1 200 x-guploader-uploadid:AAO2VwpaNSn986BwJi8tRlsbh1LLdgqNqa1KcEpfiuIv0G7Ry2U7qPQ9N-baQSJkLFnxLUIP vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:13 GMT expires:Wed, 28 May 2025 01:57:13 GMT accept-ranges:bytes x-goog-hash:crc32c=phKVxw== content-length:883094 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY��ik          021_download,6b655e64-d470-4522-a78f-7e1cd2703714�
�
$6b655e64-d470-4522-a78f-7e1cd2703714
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   3 2 1 1 4 2 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 b 6 5 5 e 6 4 - d 4 7 0 - 4 5 2 2 - a 7 8 f - 7 e 1 c d 2 7 0 3 7 1 4   x ��ۮ������������� �� �� � � � � ����������������������


     �9�Բl          021_download,d25ee089-d7f4-4a10-a055-8b34b296a8e1�
�
$d25ee089-d7f4-4a10-a055-8b34b296a8e1
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ��ܮ������������� �� � � � � � � ����������������������


     �}�<-m          &9_d25ee089-d7f4-4a10-a055-8b34b296a8e1�	$d25ee089-d7f4-4a10-a055-8b34b296a8e1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\d25ee089-d7f4-4a10-a055-8b34b296a8e18��Ԫ��@ HPϟ�/X ` p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:AAO2Vwq8rdf6ddU77ZydUwiwItYWIhGsVh5nnEaXOuuPrASOABm4K7ljY5AwB1J81gB5jYmd vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:13 GMT expires:Wed, 28 May 2025 01:57:13 GMT accept-ranges:bytes x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2��BE n          021_download,d25ee089-d7f4-4a10-a055-8b34b296a8e1�
�
$d25ee089-d7f4-4a10-a055-8b34b296a8e1
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   5 9 3 7 0 6 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 2 5 e e 0 8 9 - d 7 f 4 - 4 a 1 0 - a 0 5 5 - 8 b 3 4 b 2 9 6 a 8 e 1   x ��ܮ������������� �� �� � � � � ����������������������


     ���47o          021_download,6b655e64-d470-4522-a78f-7e1cd2703714�
�
$6b655e64-d470-4522-a78f-7e1cd2703714
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 b 6 5 5 e 6 4 - d 4 7 0 - 4 5 2 2 - a 7 8 f - 7 e 1 c d 2 7 0 3 7 1 4   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 b 6 5 5 e 6 4 - d 4 7 0 - 4 5 2 2 - a 7 8 f - 7 e 1 c d 2 7 0 3 7 1 4   x��5��ۮ����߮��� �x����c��[h:�S�Q���;"�?Ι#��5���� � � � � ����������������������


     ,L��hp          &9_6b655e64-d470-4522-a78f-7e1cd2703714�	$6b655e64-d470-4522-a78f-7e1cd2703714��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6b655e64-d470-4522-a78f-7e1cd27037148���Ԫ��@��ժ��HPϟ�/X��5`��ժ��p x �phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY��HTTP/1.1 200 x-guploader-uploadid:AAO2VwpaNSn986BwJi8tRlsbh1LLdgqNqa1KcEpfiuIv0G7Ry2U7qPQ9N-baQSJkLFnxLUIP vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:13 GMT expires:Wed, 28 May 2025 01:57:13 GMT accept-ranges:bytes x-goog-hash:crc32c=phKVxw== content-length:883094 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY���gdq          &9_0c543083-fc44-48da-8d45-249cc51d6baa�	$0c543083-fc44-48da-8d45-249cc51d6baa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0c543083-fc44-48da-8d45-249cc51d6baa8���Ԫ��@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS����dr          &9_0c543083-fc44-48da-8d45-249cc51d6baa�	$0c543083-fc44-48da-8d45-249cc51d6baa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0c543083-fc44-48da-8d45-249cc51d6baa8���Ԫ��@ HPϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSF�5Z7s          021_download,6b655e64-d470-4522-a78f-7e1cd2703714�
�
$6b655e64-d470-4522-a78f-7e1cd2703714
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 b 6 5 5 e 6 4 - d 4 7 0 - 4 5 2 2 - a 7 8 f - 7 e 1 c d 2 7 0 3 7 1 4   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 b 6 5 5 e 6 4 - d 4 7 0 - 4 5 2 2 - a 7 8 f - 7 e 1 c d 2 7 0 3 7 1 4   x��5��ۮ����߮��� �x����c��[h:�S�Q���;"�?Ι#��5���� � � � � ����������������������


     �a> t           021_download,6b655e64-d470-4522-a78f-7e1cd2703714�2>��u          021_download,0c543083-fc44-48da-8d45-249cc51d6baa�
�
$0c543083-fc44-48da-8d45-249cc51d6baa
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x ��஁������������ �� � � � � � � ����������������������


     021_download,d25ee089-d7f4-4a10-a055-8b34b296a8e1�
�
$d25ee089-d7f4-4a10-a055-8b34b296a8e1
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   5 9 3 7 0 6 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 2 5 e e 0 8 9 - d 7 f 4 - 4 a 1 0 - a 0 5 5 - 8 b 3 4 b 2 9 6 a 8 e 1   x�����ܮ������������� {�?�Ǩ�O�ƺ����f.RMH����=��� �� � � � � ����������������������


     �<&��w          &9_0c543083-fc44-48da-8d45-249cc51d6baa�	$0c543083-fc44-48da-8d45-249cc51d6baa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0c543083-fc44-48da-8d45-249cc51d6baa8���Ԫ��@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:AAO2VwqyFUnzLGJ3Hse7730DELj6OdAwPyqJ-9oexcW9KnplQ1SZJorYh9v8O_eGM7LvjZn2 cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:13 GMT expires:Wed, 28 May 2025 01:57:13 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=F4ov8g== content-length:45157 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��K x          021_download,0c543083-fc44-48da-8d45-249cc51d6baa�
�
$0c543083-fc44-48da-8d45-249cc51d6baa
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   1 7 3 6 7 2 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 c 5 4 3 0 8 3 - f c 4 4 - 4 8 d a - 8 d 4 5 - 2 4 9 c c 5 1 d 6 b a a   x ��஁������������ �� �� � � � � ����������������������


     �3a,Ky          021_download,0c543083-fc44-48da-8d45-249cc51d6baa�
�
$0c543083-fc44-48da-8d45-249cc51d6baa
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 c 5 4 3 0 8 3 - f c 4 4 - 4 8 d a - 8 d 4 5 - 2 4 9 c c 5 1 d 6 b a a   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 c 5 4 3 0 8 3 - f c 4 4 - 4 8 d a - 8 d 4 5 - 2 4 9 c c 5 1 d 6 b a a   x����஁���ᮁ�� ���1�muZ��ct�3!��0�/������=���� � � � � ����������������������


     �.���z          &9_0c543083-fc44-48da-8d45-249cc51d6baa�	$0c543083-fc44-48da-8d45-249cc51d6baa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0c543083-fc44-48da-8d45-249cc51d6baa8���Ԫ��@�Ǭժ��HPϟ�/X��`�Ǭժ��p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:AAO2VwqyFUnzLGJ3Hse7730DELj6OdAwPyqJ-9oexcW9KnplQ1SZJorYh9v8O_eGM7LvjZn2 cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:13 GMT expires:Wed, 28 May 2025 01:57:13 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=F4ov8g== content-length:45157 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��m�G{          &9_1ece3d62-c815-4f05-b86d-010a2b4a19a9�	$1ece3d62-c815-4f05-b86d-010a2b4a19a9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1ece3d62-c815-4f05-b86d-010a2b4a19a98���Ԫ��@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��cG|          &9_1ece3d62-c815-4f05-b86d-010a2b4a19a9�	$1ece3d62-c815-4f05-b86d-010a2b4a19a9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1ece3d62-c815-4f05-b86d-010a2b4a19a98���Ԫ��@ HPϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGӗ�[K}          021_download,0c543083-fc44-48da-8d45-249cc51d6baa�
�
$0c543083-fc44-48da-8d45-249cc51d6baa
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745852517&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 c 5 4 3 0 8 3 - f c 4 4 - 4 8 d a - 8 d 4 5 - 2 4 9 c c 5 1 d 6 b a a   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 c 5 4 3 0 8 3 - f c 4 4 - 4 8 d a - 8 d 4 5 - 2 4 9 c c 5 1 d 6 b a a   x����஁���ᮁ�� ���1�muZ��ct�3!��0�/������=���� � � � � ����������������������


     q��O> ~           021_download,0c543083-fc44-48da-8d45-249cc51d6baa�-	/          021_download,d25ee089-d7f4-4a10-a055-8b34b296a8e1�
�
$d25ee089-d7f4-4a10-a055-8b34b296a8e1
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 2 5 e e 0 8 9 - d 7 f 4 - 4 a 1 0 - a 0 5 5 - 8 b 3 4 b 2 9 6 a 8 e 1   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 2 5 e e 0 8 9 - d 7 f 4 - 4 a 1 0 - a 0 5 5 - 8 b 3 4 b 2 9 6 a 8 e 1   x�����ܮ����⮁�� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     j3��>�          &9_d25ee089-d7f4-4a10-a055-8b34b296a8e1�	$d25ee089-d7f4-4a10-a055-8b34b296a8e1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\d25ee089-d7f4-4a10-a055-8b34b296a8e18��Ԫ��@���ժ��HPϟ�/X���`���ժ��p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:AAO2Vwq8rdf6ddU77ZydUwiwItYWIhGsVh5nnEaXOuuPrASOABm4K7ljY5AwB1J81gB5jYmd vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:13 GMT expires:Wed, 28 May 2025 01:57:13 GMT accept-ranges:bytes x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2��ǨE�          &9_7c6b7370-5641-4756-99a6-30505544c92b�	$7c6b7370-5641-4756-99a6-30505544c92b��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\7c6b7370-5641-4756-99a6-30505544c92b8Ţ�Ԫ��@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING,b�\E�          &9_7c6b7370-5641-4756-99a6-30505544c92b�	$7c6b7370-5641-4756-99a6-30505544c92b��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\7c6b7370-5641-4756-99a6-30505544c92b8Ţ�Ԫ��@ HPϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING^���/�          021_download,d25ee089-d7f4-4a10-a055-8b34b296a8e1�
�
$d25ee089-d7f4-4a10-a055-8b34b296a8e1
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 2 5 e e 0 8 9 - d 7 f 4 - 4 a 1 0 - a 0 5 5 - 8 b 3 4 b 2 9 6 a 8 e 1   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 2 5 e e 0 8 9 - d 7 f 4 - 4 a 1 0 - a 0 5 5 - 8 b 3 4 b 2 9 6 a 8 e 1   x�����ܮ����⮁�� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     �	��> �           021_download,d25ee089-d7f4-4a10-a055-8b34b296a8e11��*��          021_download,1ece3d62-c815-4f05-b86d-010a2b4a19a9�
�
$1ece3d62-c815-4f05-b86d-010a2b4a19a9
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ��⮁������������ �� � � � � � � ����������������������


     K��Q�          &9_1ece3d62-c815-4f05-b86d-010a2b4a19a9�	$1ece3d62-c815-4f05-b86d-010a2b4a19a9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1ece3d62-c815-4f05-b86d-010a2b4a19a98���Ԫ��@ HPϟ�/X ` p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:AAO2Vwp5ydGtU-b2CRW3fBj4BphI1wFy6JKecGctIJdT3WV7q-21gbZdfa_i4EIjwXkFHXXv vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:14 GMT expires:Wed, 28 May 2025 01:57:14 GMT accept-ranges:bytes x-goog-hash:crc32c=Y3uc2g== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��E��          021_download,1ece3d62-c815-4f05-b86d-010a2b4a19a9�
�
$1ece3d62-c815-4f05-b86d-010a2b4a19a9
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   2 8 6 0 8 0 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 e c e 3 d 6 2 - c 8 1 5 - 4 f 0 5 - b 8 6 d - 0 1 0 a 2 b 4 a 1 9 a 9   x ��⮁������������ �� �� � � � � ����������������������


     [�S���          021_download,7c6b7370-5641-4756-99a6-30505544c92b�
�
$7c6b7370-5641-4756-99a6-30505544c92b
���������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�$Z	text/htmlb	text/htmlj       r       x ��㮁������������ �� � � � � � � ����������������������


     �x^�b�          &9_7c6b7370-5641-4756-99a6-30505544c92b�	$7c6b7370-5641-4756-99a6-30505544c92b��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\7c6b7370-5641-4756-99a6-30505544c92b8Ţ�Ԫ��@ HPϟ�/X ` p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:AAO2VwpR99W7_Vyf7AX8oUmrBwRrVlGEHyknvtktfVBSz5Kqlm5NVaOANw7CJmaHEd42Fcxl date:Tue, 27 May 2025 01:57:14 GMT expires:Wed, 28 May 2025 01:57:14 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=Z6DaGA== content-length:4680 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING�����          021_download,7c6b7370-5641-4756-99a6-30505544c92b�
�
$7c6b7370-5641-4756-99a6-30505544c92b
���������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�$Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   6 0 0 8 6 4 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 c 6 b 7 3 7 0 - 5 6 4 1 - 4 7 5 6 - 9 9 a 6 - 3 0 5 0 5 5 4 4 c 9 2 b   x�$��_� u 㮁������������ ��0���r�*�:���X�t����V���� �� � � � � ����������������������


     ]J29�          021_download,7c6b7370-5641-4756-99a6-30505544c92b�
�
$7c6b7370-5641-4756-99a6-30505544c92b
���������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�$Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 c 6 b 7 3 7 0 - 5 6 4 1 - 4 7 5 6 - 9 9 a 6 - 3 0 5 0 5 5 4 4 c 9 2 b   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 c 6 b 7 3 7 0 - 5 6 4 1 - 4 7 5 6 - 9 9 a 6 - 3 0 5 0 5 5 4 4 c 9 2 b   x�$��㮁���䮁�� ��0���r�*�:���X�t����V������ � � � � ����������������������


     ��qq�          &9_7c6b7370-5641-4756-99a6-30505544c92b�	$7c6b7370-5641-4756-99a6-30505544c92b��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\7c6b7370-5641-4756-99a6-30505544c92b8Ţ�Ԫ��@���ժ��HPϟ�/X�$`���ժ��p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:AAO2VwpR99W7_Vyf7AX8oUmrBwRrVlGEHyknvtktfVBSz5Kqlm5NVaOANw7CJmaHEd42Fcxl date:Tue, 27 May 2025 01:57:14 GMT expires:Wed, 28 May 2025 01:57:14 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=Z6DaGA== content-length:4680 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��
�^�          &9_ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�	$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\ad42e475-75a7-48cb-84ba-00b2cb9cb5fd8��Ԫ��@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION+�#^�          &9_ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�	$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\ad42e475-75a7-48cb-84ba-00b2cb9cb5fd8��Ԫ��@ HPϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION�K �9�          021_download,7c6b7370-5641-4756-99a6-30505544c92b�
�
$7c6b7370-5641-4756-99a6-30505544c92b
���������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�$Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 c 6 b 7 3 7 0 - 5 6 4 1 - 4 7 5 6 - 9 9 a 6 - 3 0 5 0 5 5 4 4 c 9 2 b   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 c 6 b 7 3 7 0 - 5 6 4 1 - 4 7 5 6 - 9 9 a 6 - 3 0 5 0 5 5 4 4 c 9 2 b   x�$��㮁���䮁�� ��0���r�*�:���X�t����V������ � � � � ����������������������


     )�o> �           021_download,7c6b7370-5641-4756-99a6-30505544c92b
�h��          021_download,ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�
�
$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x ��宁������������ �� � � � � � � ����������������������


     ��ڋ�          &9_ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�	$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\ad42e475-75a7-48cb-84ba-00b2cb9cb5fd8��Ԫ��@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:AAO2VwqGwm2bGw2Nlqh77nmGSB8wKg4lL85OLnWCG9GhHHu9d9JzrulwwtCtA-cbUiXrneWx accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:14 GMT expires:Wed, 28 May 2025 01:57:14 GMT x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION�4���          021_download,ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�
�
$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   1 0 1 4 6 0 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a d 4 2 e 4 7 5 - 7 5 a 7 - 4 8 c b - 8 4 b a - 0 0 b 2 c b 9 c b 5 f d   x ��宁������������ �� �� � � � � ����������������������


     J���r�          021_download,1ece3d62-c815-4f05-b86d-010a2b4a19a9�
�
$1ece3d62-c815-4f05-b86d-010a2b4a19a9
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj�$  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ O n a y l a n m a y a n   2 8 6 0 8 0 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 e c e 3 d 6 2 - c 8 1 5 - 4 f 0 5 - b 8 6 d - 0 1 0 a 2 b 4 a 1 9 a 9   x�֧��⮁������������ �+d�?4�}���)����[�h�rw|�)T��.�� �� � � � � ����������������������


     021_download,ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�
�
$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a d 4 2 e 4 7 5 - 7 5 a 7 - 4 8 c b - 8 4 b a - 0 0 b 2 c b 9 c b 5 f d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a d 4 2 e 4 7 5 - 7 5 a 7 - 4 8 c b - 8 4 b a - 0 0 b 2 c b 9 c b 5 f d   x����宁���箁�� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     
Lc���          &9_ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�	$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\ad42e475-75a7-48cb-84ba-00b2cb9cb5fd8��Ԫ��@���ժ��HPϟ�/X��`���ժ��p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:AAO2VwqGwm2bGw2Nlqh77nmGSB8wKg4lL85OLnWCG9GhHHu9d9JzrulwwtCtA-cbUiXrneWx accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:14 GMT expires:Wed, 28 May 2025 01:57:14 GMT x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONtluYI�          021_download,ad42e475-75a7-48cb-84ba-00b2cb9cb5fd�
�
$ad42e475-75a7-48cb-84ba-00b2cb9cb5fd
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a d 4 2 e 4 7 5 - 7 5 a 7 - 4 8 c b - 8 4 b a - 0 0 b 2 c b 9 c b 5 f d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a d 4 2 e 4 7 5 - 7 5 a 7 - 4 8 c b - 8 4 b a - 0 0 b 2 c b 9 c b 5 f d   x����宁���箁�� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     qf��> �           021_download,ad42e475-75a7-48cb-84ba-00b2cb9cb5fdT�֛F�          	39_config�
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1
�ހ���`�ԓ �ٖ�1
"�������d�ԓ �ٖ�1(���ʖ����
ۯ��Њ���ԓ �ٖ�1
��՛�����ԓ �ٖ�1
���Åօ�C�ԓ �ٖ�1
�����Ӆ���ԓ �ٖ�1
��������_�ԓ �ٖ�1
�����������I �ٖ�1
�������I �ٖ�1
ʒ���қ�C��I �ٖ�1
���޾���,��I �ٖ�1
���������I �ٖ�1
������t�ԓ �ٖ�1
��������k�ԓ �ٖ�1
գ��������ԓ �ٖ�1
��ר�ٳ���ԓ �ٖ�1
ෛ�������ԓ �ٖ�1
������Ʉ��ԓ �ٖ�1
"��ї�Z�ԓ �ٖ�1(Ȏ�������
#�򖐩�����ԓ �ٖ�1(Ȏ�������
#�ɕԺ����ԓ �ٖ�1(Ȏ�������
!������վN�ԓ �ٖ�1(��������'
"��������ԓ �ٖ�1(��������'
"��ڀ����ԓ �ٖ�1(��������'
!���䍟��B�ԓ �ٖ�1(��������'
"����̂呮�ԓ �ٖ�1(��������'
#������Ơ��ԓ �ٖ�1(��袺ص��
#�풠�����ԓ �ٖ�1(��袺ص��
!�����Ù��ԓ �ٖ�1(�ٴ�ڥ�7
!���������ԓ �ٖ�1(�ٴ�ڥ�7
!��ޚ�đ�y�ԓ �ٖ�1(�ٴ�ڥ�7
!������ڷu�ԓ �ٖ�1(�ٴ�ڥ�7�+ڇd�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��4>�          021_download,1ece3d62-c815-4f05-b86d-010a2b4a19a9�
�
$1ece3d62-c815-4f05-b86d-010a2b4a19a9
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 e c e 3 d 6 2 - c 8 1 5 - 4 f 0 5 - b 8 6 d - 0 1 0 a 2 b 4 a 1 9 a 9   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 e c e 3 d 6 2 - c 8 1 5 - 4 f 0 5 - b 8 6 d - 0 1 0 a 2 b 4 a 1 9 a 9   x�֧��⮁���鮁�� �+d�?4�}���)����[�h�rw|�)T��.���� � � � � ����������������������


     t'r�b�          &9_1ece3d62-c815-4f05-b86d-010a2b4a19a9�	$1ece3d62-c815-4f05-b86d-010a2b4a19a9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1ece3d62-c815-4f05-b86d-010a2b4a19a98���Ԫ��@���ժ��HPϟ�/X�֧`���ժ��p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:AAO2Vwp5ydGtU-b2CRW3fBj4BphI1wFy6JKecGctIJdT3WV7q-21gbZdfa_i4EIjwXkFHXXv vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 01:57:14 GMT expires:Wed, 28 May 2025 01:57:14 GMT accept-ranges:bytes x-goog-hash:crc32c=Y3uc2g== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING 8�>�          021_download,1ece3d62-c815-4f05-b86d-010a2b4a19a9�
�
$1ece3d62-c815-4f05-b86d-010a2b4a19a9
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1745938983&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 e c e 3 d 6 2 - c 8 1 5 - 4 f 0 5 - b 8 6 d - 0 1 0 a 2 b 4 a 1 9 a 9   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 e c e 3 d 6 2 - c 8 1 5 - 4 f 0 5 - b 8 6 d - 0 1 0 a 2 b 4 a 1 9 a 9   x�֧��⮁���鮁�� �+d�?4�}���)����[�h�rw|�)T��.���� � � � � ����������������������


     �#!�> �           021_download,1ece3d62-c815-4f05-b86d-010a2b4a19a9�tY� �           �tY� �           �tY� �           8By� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10S����          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�{�� �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10���� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10Yt�i` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�&�	��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10����6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�y?a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�J�W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10���?� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10:GD!��          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012;��g' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10!�� �           !�� �           !�� �           !�� �           fcx� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10r����          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10,+V �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10w�K�� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�3~1` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10���          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10��k6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�~�a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10W��W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10���� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10^�Lld�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�< �           �< �           �< �           �< �           !Y�O� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�t3���          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10Ҝ �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10_��� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10'�[` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10J�:2��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10=�Y�6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�109��2a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10����W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�q�� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10��@ �           5�[�          &9_02db36a2-699e-4407-9d72-6cdb54cc2b50�	$02db36a2-699e-4407-9d72-6cdb54cc2b50��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\02db36a2-699e-4407-9d72-6cdb54cc2b508��٩���@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST�K�[�          &9_02db36a2-699e-4407-9d72-6cdb54cc2b50�	$02db36a2-699e-4407-9d72-6cdb54cc2b50��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\02db36a2-699e-4407-9d72-6cdb54cc2b508��٩���@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST`�
[�          &9_02db36a2-699e-4407-9d72-6cdb54cc2b50�	$02db36a2-699e-4407-9d72-6cdb54cc2b50��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\02db36a2-699e-4407-9d72-6cdb54cc2b508��٩���@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST!sBy[�          &9_02db36a2-699e-4407-9d72-6cdb54cc2b50�	$02db36a2-699e-4407-9d72-6cdb54cc2b50��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\02db36a2-699e-4407-9d72-6cdb54cc2b508��٩���@ HPϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST-R��d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10 \���          021_download,02db36a2-699e-4407-9d72-6cdb54cc2b50�
�
$02db36a2-699e-4407-9d72-6cdb54cc2b50
�����汗�"�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ���Ɂ������������ �� � � � � � � ����������������������


     �%�v�          &9_02db36a2-699e-4407-9d72-6cdb54cc2b50�	$02db36a2-699e-4407-9d72-6cdb54cc2b50��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\02db36a2-699e-4407-9d72-6cdb54cc2b508��٩���@ HPϟ�/X ` p x �https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST��HTTP/1.1 200 x-guploader-uploadid:ABgVH88YHyDCoJVIM_5g2sxIYpYVjSXBvQAHc4m1ahI8ANpL4cX1_83nl-Q_qrl5BYsa7_WQeb2HFkA date:Tue, 27 May 2025 02:04:39 GMT expires:Wed, 28 May 2025 02:04:39 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=aDJGWw== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST	���          021_download,02db36a2-699e-4407-9d72-6cdb54cc2b50�
�
$02db36a2-699e-4407-9d72-6cdb54cc2b50
�����汗�"�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   5 4 2 5 4 1 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d b 3 6 a 2 - 6 9 9 e - 4 4 0 7 - 9 d 7 2 - 6 c d b 5 4 c c 2 b 5 0   x ���Ɂ������������ �� �� � � � � ����������������������


     �T�H�          021_download,02db36a2-699e-4407-9d72-6cdb54cc2b50�
�
$02db36a2-699e-4407-9d72-6cdb54cc2b50
�����汗�"�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�ֶZ	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d b 3 6 a 2 - 6 9 9 e - 4 4 0 7 - 9 d 7 2 - 6 c d b 5 4 c c 2 b 5 0   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d b 3 6 a 2 - 6 9 9 e - 4 4 0 7 - 9 d 7 2 - 6 c d b 5 4 c c 2 b 5 0   x�ֶ���Ɂ���ʁ�� ݜ泮Y��'���<�/n�D��Y �4������ � � � � ����������������������


     ��Ƈ�          &9_02db36a2-699e-4407-9d72-6cdb54cc2b50�	$02db36a2-699e-4407-9d72-6cdb54cc2b50��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\02db36a2-699e-4407-9d72-6cdb54cc2b508��٩���@�ߖ����HPϟ�/X�ֶ`�ߖ����p x �https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST��HTTP/1.1 200 x-guploader-uploadid:ABgVH88YHyDCoJVIM_5g2sxIYpYVjSXBvQAHc4m1ahI8ANpL4cX1_83nl-Q_qrl5BYsa7_WQeb2HFkA date:Tue, 27 May 2025 02:04:39 GMT expires:Wed, 28 May 2025 02:04:39 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=aDJGWw== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST�yF�H�          021_download,02db36a2-699e-4407-9d72-6cdb54cc2b50�
�
$02db36a2-699e-4407-9d72-6cdb54cc2b50
�����汗�"�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�ֶZ	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d b 3 6 a 2 - 6 9 9 e - 4 4 0 7 - 9 d 7 2 - 6 c d b 5 4 c c 2 b 5 0   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d b 3 6 a 2 - 6 9 9 e - 4 4 0 7 - 9 d 7 2 - 6 c d b 5 4 c c 2 b 5 0   x�ֶ���Ɂ���ʁ�� ݜ泮Y��'���<�/n�D��Y �4������ � � � � ����������������������


     �iHf> �           021_download,02db36a2-699e-4407-9d72-6cdb54cc2b50�<�(a �          )3_PasswordsManagementBubbleAfterSave_used)
'PasswordsManagementBubbleAfterSave_used� Ki �          )3_PasswordsManagementBubbleAfterSave_used1
'PasswordsManagementBubbleAfterSave_used�����g �          ,3_PasswordsManagementBubbleDuringSignin_used,
*PasswordsManagementBubbleDuringSignin_used�APo �          ,3_PasswordsManagementBubbleDuringSignin_used4
*PasswordsManagementBubbleDuringSignin_used�����9 �          #38_h       k�l��,�   E��   E��
 �:]'M�9 �          #38_e       ���w���   E��   E��
 �:�85g9 �          #38_e       �M����   E��   E��
�:@��@ �     �=�5      #38_h       OG�A<T   E��   E��	
���:�¯� �           �¯� �           �¯� �           ��*� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10��֐�          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10��p �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10ָR+� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10 �` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10<ؑy��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10��F 6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�y�a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10k���W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10���� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10���d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��7 �           ��7 �           ��7 �           ��7 �           V��� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10~񾲐�          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10��С �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�Q�� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10+m]` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10{��E��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10{h��6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�v�|a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�>ďW�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�뛫� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�w3d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10C� �           C� �           C� �           C� �           u�!֒ �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�fD-��          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�	 �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�1002W� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10UF˞` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��U��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�PC6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10���;a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10O%�OW�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10��r	� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10!���          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122*��� 849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��ĭ �           ��ĭ �           ��ĭ �           ��ĭ �           /8�� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10k�x��          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�� �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10���N� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10u�c�` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10g!&��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�6�6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�/Ka�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10��w5W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�a<� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10zFP�d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�103�� �           3�� �           3�� �           3�� �           �#��� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�+�ː          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�1��          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�X?��          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�?��`          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�m&��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�J�D6         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�101p6�a         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10P7�W         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10X蘠�          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10"7Hd	         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��&� 
          u1u� 
         20_1_2
509 �vF%           �vF%           �vF%           �vF%           �vF%           �vF%           �vF%           �vF%           o����          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10⢠��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10
B� 
         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�,��          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10O��`          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�����         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10���6         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10!5��a         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�X�W         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�4wd�          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10���u�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_url�펜hids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�6j8           �6j8           �6j8           �6j8           5z�|�          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10'[1�         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10 ���          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10l)���          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10o9(o`          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10Y Jh�         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10���6         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10���a         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10��W         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�P���          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�C�d          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�/� !          �/� !          �/� !          �/� !          �?h;� !         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10,���"         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10Y�ur #         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10hH�8� $         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10F�A�` %         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�Q�(�&         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10��6'         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10i���a(         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10~E*W)         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10`zt�� *         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�kI�d+         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��1^ ,          ��1^ ,          ��1^ ,          ��1^ ,          ���J� ,         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10��m]�-         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�]�� .         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�y�� /         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10f͚�` 0         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10,L��1         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�103�62         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10����a3         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�;�6W4         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�107X�� 5         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�&0�6         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11B��D';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�8N  7          �8N  7          �8N  7          �8N  7          ���� 7         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10 Pѐ8         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�102��� 9         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10���-� :         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��` ;         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�+�<         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10���j6=         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10i�o�a>         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10���W?         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�FH� @         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�A@�dA         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10Q&[� B          Q&[� B          Q&[� B          Q&[� B          ���b� B         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�RꊐC         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�100gE D         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10ح��� E         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10[R�` F         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�:V�G         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10Cx�A6H         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10��� aI         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10���`WJ         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10Q_��� K         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�|ZdL         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10���U M          U�7I. M         22_1|360x640|30�  9T���pyB
�=� N          
�=� N          
�=� N          "�� N         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10���<�O         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�i�� P         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10D���� Q         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�105�
` R         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10 ���S         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�3 �6T         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10Ab�aU         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10ޱWV         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10��G�� W         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10��r�X         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUMw�N�a(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��@E Y          7�YO. Y         22_1|360x640|30�  9^Hi��pyB� �� Z          � �� Z          � �� Z          5�� Z         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10e����[         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�Yq� \         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10 �6�� ]         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��;`` ^         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��A�_         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�`�6`         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�@y�aa         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10q7S�Wb         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�-j� c         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�B�dd         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10L^8  e          L^8  e          L^8  e          L^8  e          a��-� e         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�3�%�f         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10LL�� g         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�!�� h         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10e"�` i         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�
!�j         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10p&��6k         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�F��al         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10]�
Wm         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10pV��� n         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10N�& o          N�& o          N�& o          �х� o         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10��9n�p         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10e��� q         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10
��k� r         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��'?` s         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10ť�/�t         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10U&>6u         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�5p�av         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10_�Ww         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10M*6�� x         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10D�te y          D�te y          D�te y          ��)Ւ y         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�101�F��z         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�.K� {         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�-�� |         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��|>` }         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��w�~         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10���6         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10Yo\�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10��{�W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10ؖ��� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10w,�d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��	|a �         '4_IPH_AutofillBnplAffirmOrZipSuggestion+
%IPH_AutofillBnplAffirmOrZipSuggestion���6�� �          �6�� �          �6�� �          [\�� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10,�d��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10C�� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�÷g� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10
�` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10W�|��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10s�jU �         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (�9���0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�e�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10zcW�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10K��� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10Ej�d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�� �          �� �          �� �          �� �          {��C� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�d���         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�105�Fo �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10%uu�� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10_�� ` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10bř��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�'6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10a�E�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�N�W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10&a�f� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10��P�d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�WV �          �WV �          �WV �          �WV �          �b� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10=���         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10s�~� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�D*� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�-` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�#���         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10Cq` 6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10���a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10T�a�W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10i���� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10����d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10[z?g �          [z?g �          [z?g �          [z?g �          ���� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10Xe�I��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10/W�@ �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10��(�� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10QN�` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��3�|�         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�Ӫ�	 ٖ�10n�P�6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10)R<�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10���W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�vl� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10ݚ+�d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10R|>� �          R|>� �          R|>� �          R|>� �          ͽԒ �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10N��Ր�         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�!z �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�0�� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�G�J` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�105����         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10jw=�6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10!�	�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10V+�W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10n��H� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10S��d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�O<- �          �O<- �          �O<- �          �O<- �          �|�� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10i�@��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10���� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10k��� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�3��` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�]!��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10��"�6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10Yl�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10ؼ�RW�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�1083� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10��s�d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10a+�n �          a+�n �          a+�n �          a+�n �          ��y� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10��7���         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10^� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�U�.� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��yr` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10ȳ��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_wE��h visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�4DJ6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10	w8�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�X��W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10���� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10k'�/ �          k'�/ �          k'�/ �          ��v�� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�I��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�105�:� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10p�3� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10-V�` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10O`�N��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10d.6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10	ʿa�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10CFW�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�ꓰ� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10���Nd�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��� �          ��� �          ��� �          ��� �          ��� �          ��� �          ��� �          ��� �          ��� �          ��ؒ �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10��1��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�c�" �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10��w�� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10w` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�Z��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10wy�6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�ZX�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10��W�W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10��N�� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10��nd�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�N �          ��t$7 �          )3_PasswordsManagementBubbleAfterSave_used� ]G: �          ,3_PasswordsManagementBubbleDuringSignin_used&�uD�      /   4_IPH_DesktopTabGroupsNewGroup"
IPH_DesktopTabGroupsNewGroup��4_IPH_HighEfficiencyInfoMode 
IPH_HighEfficiencyInfoMode��4_IPH_TabAudioMuting
IPH_TabAudioMuting��4_IPH_PasswordsAccountStorage!
IPH_PasswordsAccountStorage��4_IPH_PerformanceNewBadge
IPH_PerformanceNewBadge��4_IPH_TabSearch

IPH_TabSearch��4_IPH_WebUITabStrip
IPH_WebUITabStrip��4_IPH_DesktopPwaInstall
IPH_DesktopPwaInstall��4_IPH_DesktopSharedHighlighting#
IPH_DesktopSharedHighlighting�� 4_EsbDownloadRowPromo 4_IPH_AutofillAiOptIn '4_IPH_AutofillBnplAffirmOrZipSuggestion .4_IPH_AutofillExternalAccountProfileSuggestion &4_IPH_AutofillVirtualCardCVCSuggestion 4_IPH_CompanionSidePanel $4_IPH_CompanionSidePanelRegionSearch 4_IPH_CookieControls 4_IPH_DesktopCustomizeChrome $4_IPH_DesktopPWAsLinkCapturingLaunch ,4_IPH_DesktopPWAsLinkCapturingLaunchAppInTab 4_IPH_DiscardRing 4_IPH_DownloadEsbPromo /4_IPH_ExplicitBrowserSigninPreferenceRemembered 4_IPH_GMCLocalMediaCasting 4_IPH_HighEfficiencyMode 4_IPH_HistorySearch 4_IPH_PasswordSharingFeature (4_IPH_PasswordsManagementBubbleAfterSave +4_IPH_PasswordsManagementBubbleDuringSignin "4_IPH_PasswordsWebAppProfileSwitch 4_IPH_PdfSearchifyFeature *4_IPH_PerformanceInterventionDialogFeature -4_IPH_PriceInsightsPageActionIconLabelFeature &4_IPH_PriceTrackingEmailConsentFeature 4_IPH_PwaQuietNotification 4_IPH_ReadingModeSidePanel 4_IPH_ShoppingCollectionFeature %4_IPH_SidePanelGenericPinnableFeature )4_IPH_SidePanelLensOverlayPinnableFeature 14_IPH_SidePanelLensOverlayPinnableFollowupFeature 4_IPH_SignoutWebIntercept !4_IPH_SupervisedUserProfileSignin 4_IPH_TabGroupsSaveV2CloseGroup 4_IPH_TabGroupsSaveV2Intro 4_IPH_iOSAddressPromoDesktop 4_IPH_iOSPasswordPromoDesktop 4_IPH_iOSPaymentPromoDesktoprL��           I��P�          37_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10Q��a           vM��          37_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�105�c�           5�c�           5�c�           ����1           #38_h       k�l��,�   E��   E��ߑ�1           #38_e       �M����   E��   E����$Y           \�[1           #38_e       ���w���   E��   E���sD�           �sD�           �sD�           �sD�           �sD�           �sD�           �sD�           �sD�           �sD�           �sD�           �sD�           �sD�           �sD�           �sD�           �sD�           C
k�F         	39_config�
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1
�ހ���`�ԓ �ٖ�1
"�������d�ԓ �ٖ�1(���ʖ����
ۯ��Њ���ԓ ����1
��՛�����ԓ �ٖ�1
���Åօ�C�ԓ �ٖ�1
�����Ӆ���ԓ �ٖ�1
��������_�ԓ �ٖ�1
�����������I �ٖ�1
�������I �ٖ�1
ʒ���қ�C��I �ٖ�1
���޾���,��I �ٖ�1
���������I �ٖ�1
������t�ԓ ����1
��������k�ԓ ����1
գ��������ԓ ����1
��ר�ٳ���ԓ ����1
ෛ�������ԓ ����1
������Ʉ��ԓ ����1
"��ї�Z�ԓ �ٖ�1(Ȏ�������
#�򖐩�����ԓ �ٖ�1(Ȏ�������
#�ɕԺ����ԓ �ٖ�1(Ȏ�������
!������վN�ԓ �ٖ�1(��������'
"��������ԓ �ٖ�1(��������'
"��ڀ����ԓ �ٖ�1(��������'
!���䍟��B�ԓ �ٖ�1(��������'
"����̂呮�ԓ �ٖ�1(��������'
#������Ơ��ԓ �ٖ�1(��袺ص��
#�풠�����ԓ �ٖ�1(��袺ص��
!�����Ù��ԓ �ٖ�1(�ٴ�ڥ�7
!���������ԓ �ٖ�1(�ٴ�ڥ�7
!��ޚ�đ�y�ԓ �ٖ�1(�ٴ�ڥ�7
!������ڷu�ԓ �ٖ�1(�ٴ�ڥ�7��           � 2�          37_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�108��           �L���          37_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�,             S�           37_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10 4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !           4Ld !          �FkNF!         	39_config�
��؈��O�ԓ �ٖ�1
����Ą���ԓ �ٖ�1
�����ٝ���ԓ �ٖ�1
�����ؿ���ԓ �ٖ�1
�ހ���`�ԓ �ٖ�1
"�������d�ԓ �ٖ�1(���ʖ����
ۯ��Њ���ԓ �Ț�1
��՛�����ԓ �ٖ�1
���Åօ�C�ԓ �ٖ�1
�����Ӆ���ԓ �ٖ�1
��������_�ԓ �ٖ�1
�����������I �ٖ�1
�������I �ٖ�1
ʒ���қ�C��I �ٖ�1
���޾���,��I �ٖ�1
���������I �ٖ�1
������t�ԓ �Ț�1
��������k�ԓ �Ț�1
գ��������ԓ �Ț�1
��ר�ٳ���ԓ �Ț�1
ෛ�������ԓ �Ț�1
������Ʉ��ԓ �Ț�1
"��ї�Z�ԓ �ٖ�1(Ȏ�������
#�򖐩�����ԓ �ٖ�1(Ȏ�������
#�ɕԺ����ԓ �ٖ�1(Ȏ�������
!������վN�ԓ �ٖ�1(��������'
"��������ԓ �ٖ�1(��������'
"��ڀ����ԓ �ٖ�1(��������'
!���䍟��B�ԓ �ٖ�1(��������'
"����̂呮�ԓ �ٖ�1(��������'
#������Ơ��ԓ �ٖ�1(��袺ص��
#�풠�����ԓ �ٖ�1(��袺ص��
!�����Ù��ԓ �ٖ�1(�ٴ�ڥ�7
!���������ԓ �ٖ�1(�ٴ�ڥ�7
!��ޚ�đ�y�ԓ �ٖ�1(�ٴ�ڥ�7
!������ڷu�ԓ �ٖ�1(�ٴ�ڥ�7D��� "          D��� "          D��� "          #���� "         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10��Y��#         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�y�� $         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10pz,�� %         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10w��` &         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�{m��'         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�P�e(         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDevic�c�� eTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�100��a)         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10[�W*         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10-�� +         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10��N	 ,          ��N	 ,          ��N	 ,          ��o\� ,         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�104C�B�-         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�I� .         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�� �� /         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10���F` 0         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�o\�1         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10π�62         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10��a3         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10��n1W4         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10*���� 5         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�BQ� 6          �BQ� 6          �BQ� 6          
��z� 6         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10%�_=�7         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10��3 8         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10S �F� 9         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10?���` :         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10I"��;         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�]e6<         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10 ��a=         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10+1�W>         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�!�u� ?         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10h�d@         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10B頛~A      /   4_EsbDownloadRowPromo
EsbDownloadRowPromo��4_IPH_CompanionSidePanel
IPH_CompanionSidePanel��$4_IPH_CompanionSidePanelRegionSearch(
"IPH_CompanionSidePanelRegionSearch��4_IPH_DesktopCustomizeChrome 
IPH_DesktopCustomizeChrome��4_IPH_DiscardRing
IPH_DiscardRing��4_IPH_DownloadEsbPromo
IPH_DownloadEsbPromo��/4_IPH_ExplicitBrowserSigninPreferenceRemembered3
-IPH_ExplicitBrowserSigninPreferenceRemembered��4_IPH_HistorySearch
IPH_HistorySearch��4_IPH_GMCLocalMediaCasting
IPH_GMCLocalMediaCasting��4_IPH_HighEfficiencyMode
IPH_HighEfficiencyMode��(4_IPH_PasswordsManagementBubbleAfterSave,
&IPH_PasswordsManagementBubbleAfterSave��+4_IPH_PasswordsManagementBubbleDuringSignin/
)IPH_PasswordsManagementBubbleDuringSignin��"4_IPH_PasswordsWebAppProfileSwitch&
 IPH_PasswordsWebAppProfileSwitch��4_IPH_PasswordSharingFeature 
IPH_PasswordSharingFeature��4_IPH_PdfSearchifyFeature
IPH_PdfSearchifyFeature��*4_IPH_PerformanceInterventionDialogFeature.
(IPH_PerformanceInterventionDialogFeature��-4_IPH_PriceInsightsPageActionIconLabelFeature1
+IPH_PriceInsightsPageActionIconLabelFeature��&4_IPH_PriceTrackingEmailConsentFeature*
$IPH_PriceTrackingEmailConsentFeature��4_IPH_ReadingModeSidePanel
IPH_ReadingModeSidePanel��4_IPH_ShoppingCollectionFeature#
IPH_ShoppingCollectionFeature��%4_IPH_SidePanelGenericPinnableFeature)
#IPH_SidePanelGenericPinnableFeature��)4_IPH_SidePanelLensOverlayPinnableFeature-
'IPH_SidePanelLensOverlayPinnableFeature��14_IPH_SidePanelLensOverlayPinnableFollowupFeature5
/IPH_SidePanelLensOverlayPinnableFollowupFeature��4_IPH_SignoutWebIntercept
IPH_SignoutWebIntercept��4_IPH_TabGroupsSaveV2Intro
IPH_TabGroupsSaveV2Intro��4_IPH_TabGroupsSaveV2CloseGroup#
IPH_TabGroupsSaveV2CloseGroup��4_IPH_PwaQuietNotification
IPH_PwaQuietNotification��4_IPH_AutofillAiOptIn
IPH_AutofillAiOptIn��'4_IPH_AutofillBnplAffirmOrZipSuggestion+
%IPH_AutofillBnplAffirmOrZipSuggestion��.4_IPH_AutofillExternalAccountProfileSuggestion2
,IPH_AutofillExternalAccountProfileSuggestion��&4_IPH_AutofillVirtualCardCVCSuggestion*
$IPH_AutofillVirtualCardCVCSuggestion��4_IPH_CookieControls
IPH_CookieControls��$4_IPH_DesktopPWAsLinkCapturingLaunch(
"IPH_DesktopPWAsLinkCapturingLaunch��,4_IPH_DesktopPWAsLinkCapturingLaunchAppInTab0
*IPH_DesktopPWAsLinkCapturingLaunchAppInTab��!4_IPH_SupervisedUserProfileSignin%
IPH_SupervisedUserProfileSignin��4_IPH_iOSPasswordPromoDesktop!
IPH_iOSPasswordPromoDesktop��4_IPH_iOSAddressPromoDesktop 
IPH_iOSAddressPromoDesktop��4_IPH_iOSPaymentPromoDesktop 
IPH_iOSPaymentPromoDesktop�� 4_IPH_DesktopPwaInstall 4_IPH_DesktopSharedHighlighting 4_IPH_DesktopTabGroupsNewGroup 4_IPH_HighEfficiencyInfoMode 4_IPH_PasswordsAccountStorage 4_IPH_PerformanceNewBadge 4_IPH_TabAudioMuting 4_IPH_TabSearch 4_IPH_WebUITabStrip�`��Cp         &9_0003c3c9-07dc-484c-abac-d024428ddafd�	$0003c3c9-07dc-484c-abac-d024428ddafd��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0003c3c9-07dc-484c-abac-d024428ddafd8֏�����@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION4KYfq         &9_2e482cd3-521e-49e2-a0ce-5db487fce8c8�	$2e482cd3-521e-49e2-a0ce-5db487fce8c8��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\2e482cd3-521e-49e2-a0ce-5db487fce8c88�������@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSj���=r         &9_1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�	$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1b6a7424-2e83-4a64-b271-6f0e7cd4ea048ߐ�����@ H Pϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITYW��;2s         &9_b09d6740-4747-4135-9f78-1dc9080e6317�	$b09d6740-4747-4135-9f78-1dc9080e6317��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\b09d6740-4747-4135-9f78-1dc9080e63178�������@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2�^��dt         &9_fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�	$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\fe1335f1-44c2-4e1c-8b18-b267bde3ddaa8ȑ�����@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSC�Gu         &9_df31e84b-307d-445c-ba63-6c0dceed6fc9�	$df31e84b-307d-445c-ba63-6c0dceed6fc9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\df31e84b-307d-445c-ba63-6c0dceed6fc98�������@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��#Cv         &9_0003c3c9-07dc-484c-abac-d024428ddafd�	$0003c3c9-07dc-484c-abac-d024428ddafd��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0003c3c9-07dc-484c-abac-d024428ddafd8֏�����@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION����Cw         &9_0003c3c9-07dc-484c-abac-d024428ddafd�	$0003c3c9-07dc-484c-abac-d024428ddafd��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0003c3c9-07dc-484c-abac-d024428ddafd8֏�����@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION*���Cx         &9_0003c3c9-07dc-484c-abac-d024428ddafd�	$0003c3c9-07dc-484c-abac-d024428ddafd��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0003c3c9-07dc-484c-abac-d024428ddafd8֏�����@ HPϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTIONP�fy         &9_2e482cd3-521e-49e2-a0ce-5db487fce8c8�	$2e482cd3-521e-49e2-a0ce-5db487fce8c8��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\2e482cd3-521e-49e2-a0ce-5db487fce8c88�������@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�&w�fz         &9_2e482cd3-521e-49e2-a0ce-5db487fce8c8�	$2e482cd3-521e-49e2-a0ce-5db487fce8c8��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\2e482cd3-521e-49e2-a0ce-5db487fce8c88�������@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��fZf{         &9_2e482cd3-521e-49e2-a0ce-5db487fce8c8�	$2e482cd3-521e-49e2-a0ce-5db487fce8c8��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\2e482cd3-521e-49e2-a0ce-5db487fce8c88�������@ HPϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSj�=|         &9_1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�	$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1b6a7424-2e83-4a64-b271-6f0e7cd4ea048ߐ�����@ H Pϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY�!2}         &9_b09d6740-4747-4135-9f78-1dc9080e6317�	$b09d6740-4747-4135-9f78-1dc9080e6317��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\b09d6740-4747-4135-9f78-1dc9080e63178�������@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2����d~         &9_fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�	$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\fe1335f1-44c2-4e1c-8b18-b267bde3ddaa8ȑ�����@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��G         &9_df31e84b-307d-445c-ba63-6c0dceed6fc9�	$df31e84b-307d-445c-ba63-6c0dceed6fc9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\df31e84b-307d-445c-ba63-6c0dceed6fc98�������@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�`���         021_download,0003c3c9-07dc-484c-abac-d024428ddafd�
�
$0003c3c9-07dc-484c-abac-d024428ddafd
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj       r       x ��̈́������������� �� � � � � � � ����������������������


     �v[Ah�         &9_0003c3c9-07dc-484c-abac-d024428ddafd�	$0003c3c9-07dc-484c-abac-d024428ddafd��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0003c3c9-07dc-484c-abac-d024428ddafd8֏�����@ HPϟ�/X ` p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH88C_PjWCWMlv0HihyAZoctlAcmdVvbV4FB-Eroktkr-bfXvkvh3tBGaVPLg-6Lvo4tsmQ-BDbQ cache-control:public, max-age=86400 date:Tue, 27 May 2025 19:49:15 GMT expires:Wed, 28 May 2025 19:49:15 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION�ͮ��         021_download,0003c3c9-07dc-484c-abac-d024428ddafd�
�
$0003c3c9-07dc-484c-abac-d024428ddafd
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   9 5 6 4 0 4 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 0 0 3 c 3 c 9 - 0 7 d c - 4 8 4 c - a b a c - d 0 2 4 4 2 8 d d a f d   x ��̈́������������� �� �� � � � � ����������������������


     5�s��         021_download,2e482cd3-521e-49e2-a0ce-5db487fce8c8�
�
$2e482cd3-521e-49e2-a0ce-5db487fce8c8
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj       r       x ��΄������������� �� � � � � � � ����������������������


     H f��         &9_2e482cd3-521e-49e2-a0ce-5db487fce8c8�	$2e482cd3-521e-49e2-a0ce-5db487fce8c8��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\2e482cd3-521e-49e2-a0ce-5db487fce8c88�������@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH893ujTUP_frRXnc_BjI186PdVdojewoE3Mv5w_v_9y_6R8Hm5t1bWd6nPRCOHxyCLYLdFrhz_Q date:Tue, 27 May 2025 19:49:15 GMT expires:Wed, 28 May 2025 19:49:15 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=�6��KLQV2Q== content-length:5158 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�a;^�         021_download,0003c3c9-07dc-484c-abac-d024428ddafd�
�
$0003c3c9-07dc-484c-abac-d024428ddafd
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   9 5 6 4 0 4 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 0 0 3 c 3 c 9 - 0 7 d c - 4 8 4 c - a b a c - d 0 2 4 4 2 8 d d a f d   x���̈́������������� �!��Q�k���֧*��:{`B��aOW�+V�@�� �� � � � � ����������������������


     021_download,2e482cd3-521e-49e2-a0ce-5db487fce8c8�
�
$2e482cd3-521e-49e2-a0ce-5db487fce8c8
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   4 4 1 1 4 7 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 2 e 4 8 2 c d 3 - 5 2 1 e - 4 9 e 2 - a 0 c e - 5 d b 4 8 7 f c e 8 c 8   x�(��΄������������� S,
�
��Ȭk�.-��[�@��u�Ka���i"��� �� � � � � ����������������������


     ����:�         021_download,0003c3c9-07dc-484c-abac-d024428ddafd�
�
$0003c3c9-07dc-484c-abac-d024428ddafd
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 0 0 3 c 3 c 9 - 0 7 d c - 4 8 4 c - a b a c - d 0 2 4 4 2 8 d d a f d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 0 0 3 c 3 c 9 - 0 7 d c - 4 8 4 c - a b a c - d 0 2 4 4 2 8 d d a f d   x���̈́����τ��� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     |x�         &9_0003c3c9-07dc-484c-abac-d024428ddafd�	$0003c3c9-07dc-484c-abac-d024428ddafd��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\0003c3c9-07dc-484c-abac-d024428ddafd8֏�����@�ƍ����HPϟ�/X�`�ƍ����p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH88C_PjWCWMlv0HihyAZoctlAcmdVvbV4FB-Eroktkr-bfXvkvh3tBGaVPLg-6Lvo4tsmQ-BDbQ cache-control:public, max-age=86400 date:Tue, 27 May 2025 19:49:15 GMT expires:Wed, 28 May 2025 19:49:15 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION��:�=�         &9_1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�	$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1b6a7424-2e83-4a64-b271-6f0e7cd4ea048ߐ�����@ H Pϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITYq==�         &9_1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�	$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1b6a7424-2e83-4a64-b271-6f0e7cd4ea048ߐ�����@ HPϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY �+0:�         021_download,0003c3c9-07dc-484c-abac-d024428ddafd�
�
$0003c3c9-07dc-484c-abac-d024428ddafd
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 0 0 3 c 3 c 9 - 0 7 d c - 4 8 4 c - a b a c - d 0 2 4 4 2 8 d d a f d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 0 0 3 c 3 c 9 - 0 7 d c - 4 8 4 c - a b a c - d 0 2 4 4 2 8 d d a f d   x���̈́����τ��� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     Ge�> �          021_download,0003c3c9-07dc-484c-abac-d024428ddafd����J�         021_download,2e482cd3-521e-49e2-a0ce-5db487fce8c8�
�
$2e482cd3-521e-49e2-a0ce-5db487fce8c8
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 2 e 4 8 2 c d 3 - 5 2 1 e - 4 9 e 2 - a 0 c e - 5 d b 4 8 7 f c e 8 c 8   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 2 e 4 8 2 c d 3 - 5 2 1 e - 4 9 e 2 - a 0 c e - 5 d b 4 8 7 f c e 8 c 8   x�(��΄����Є��� S,
�
��Ȭk�.-��[�@��u�Ka���i"����� � � � � ����������������������


     �����         &9_2e482cd3-521e-49e2-a0ce-5db487fce8c8�	$2e482cd3-521e-49e2-a0ce-5db487fce8c8��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\2e482cd3-521e-49e2-a0ce-5db487fce8c88�������@۟�����HPϟ�/X�(`۟�����p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH893ujTUP_frRXnc_BjI186PdVdojewoE3Mv5w_v_9y_6R8Hm5t1bWd6nPRCOHxyCLYLdFrhz_Q date:Tue, 27 May 2025 19:49:15 GMT expires:Wed, 28 May 2025 19:49:15 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=KLQV2Q== content-length:5158 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSC��2�         &9_b09d6740-4747-4135-9f78-1dc9080e6317�	$b09d6740-4747-4135-9f78-1dc9080e6317��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\b09d6740-4747-4135-9f78-1dc9080e63178�������@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2Q�X2�         &9_b09d6740-4747-4135-9f78-1dc9080e6317�	$b09d6740-4747-4135-9f78-1dc9080e6317��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\b09d6740-4747-4135-9f78-1dc9080e63178�������@ HPϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2�j�J�         021_download,2e482cd3-521e-49e2-a0ce-5db487fce8c8�
�
$2e482cd3-521e-49e2-a0ce-5db487fce8c8
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 2 e 4 8 2 c d 3 - 5 2 1 e - 4 9 e 2 - a 0 c e - 5 d b 4 8 7 f c e 8 c 8   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 2 e 4 8 2 c d 3 - 5 2 1 e - 4 9 e 2 - a 0 c e - 5 d b 4 8 7 f c e 8 c 8   x�(��΄����Є��� S,
�
��Ȭk�.-��[�@��u�Ka���i"����� � � � � ����������������������


     u���> �          021_download,2e482cd3-521e-49e2-a0ce-5db487fce8c8"���         021_download,1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�
�
$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj       r       x ��Є������������� �� � � � � � � ����������������������


     �?� _�         &9_1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�	$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1b6a7424-2e83-4a64-b271-6f0e7cd4ea048ߐ�����@ HPϟ�/X ` p x �phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_rl3rYuk4xCJszMDLQPlPQ4CWNM7AfML5exgRtjiT55ZpszKdG4qYTxe6LeoAaQudtR6wBajA expires:Wed, 28 May 2025 19:49:15 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 19:49:15 GMT x-goog-hash:crc32c=phKVxw== content-length:883094 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY�~�W�         021_download,1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�
�
$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   7 5 6 8 8 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 6 a 7 4 2 4 - 2 e 8 3 - 4 a 6 4 - b 2 7 1 - 6 f 0 e 7 c d 4 e a 0 4   x ��Є������������� �� �� � � � � ����������������������


     �>�Ų�         021_download,b09d6740-4747-4135-9f78-1dc9080e6317�
�
$b09d6740-4747-4135-9f78-1dc9080e6317
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ��Є������������� �� � � � � � � ����������������������


     N���4�         &9_b09d6740-4747-4135-9f78-1dc9080e6317�	$b09d6740-4747-4135-9f78-1dc9080e6317��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\b09d6740-4747-4135-9f78-1dc9080e63178�������@ HPϟ�/X ` p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_nSRsc-K_RJDASS4Hcp_cpCwJpkuwzOXz-fA6khrkKCoEWGI7vvPsV0JK_Zi3X_n1ueRRSwQk date:Tue, 27 May 2025 19:49:15 GMT expires:Wed, 28 May 2025 19:49:15 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2��vf��         021_download,b09d6740-4747-4135-9f78-1dc9080e6317�
�
$b09d6740-4747-4135-9f78-1dc9080e6317
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   8 1 5 7 3 3 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 0 9 d 6 7 4 0 - 4 7 4 7 - 4 1 3 5 - 9 f 7 8 - 1 d c 9 0 8 0 e 6 3 1 7   x ��Є������������� �� �� � � � � ����������������������


     �H�M�         021_download,1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�
�
$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 6 a 7 4 2 4 - 2 e 8 3 - 4 a 6 4 - b 2 7 1 - 6 f 0 e 7 c d 4 e a 0 4   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 6 a 7 4 2 4 - 2 e 8 3 - 4 a 6 4 - b 2 7 1 - 6 f 0 e 7 c d 4 e a 0 4   x��5��Є����ӄ��� �x����c��[h:�S�Q���;"�?Ι#��5���� � � � � ����������������������


     021_download,b09d6740-4747-4135-9f78-1dc9080e6317�
�
$b09d6740-4747-4135-9f78-1dc9080e6317
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   8 1 5 7 3 3 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 0 9 d 6 7 4 0 - 4 7 4 7 - 4 1 3 5 - 9 f 7 8 - 1 d c 9 0 8 0 e 6 3 1 7   x�����Є������������� {�?�Ǩ�O�ƺ����f.RMH����=��� �� � � � � ����������������������


     �w�@o�         &9_1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�	$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\1b6a7424-2e83-4a64-b271-6f0e7cd4ea048ߐ�����@�������HPϟ�/X��5`�������p x �phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_rl3rYuk4xCJszMDLQPlPQ4CWNM7AfML5exgRtjiT55ZpszKdG4qYTxe6LeoAaQudtR6wBajA expires:Wed, 28 May 2025 19:49:15 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 19:49:15 GMT x-goog-hash:crc32c=phKVxw== content-length:883094 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY{�>d�         &9_fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�	$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\fe1335f1-44c2-4e1c-8b18-b267bde3ddaa8ȑ�����@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS���d�         &9_fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�	$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\fe1335f1-44c2-4e1c-8b18-b267bde3ddaa8ȑ�����@ HPϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS�E�"7�         021_download,1b6a7424-2e83-4a64-b271-6f0e7cd4ea04�
�
$1b6a7424-2e83-4a64-b271-6f0e7cd4ea04
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 6 a 7 4 2 4 - 2 e 8 3 - 4 a 6 4 - b 2 7 1 - 6 f 0 e 7 c d 4 e a 0 4   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 6 a 7 4 2 4 - 2 e 8 3 - 4 a 6 4 - b 2 7 1 - 6 f 0 e 7 c d 4 e a 0 4   x��5��Є����ӄ��� �x����c��[h:�S�Q���;"�?Ι#��5���� � � � � ����������������������


     ���[> �          021_download,1b6a7424-2e83-4a64-b271-6f0e7cd4ea04QkUe/�         021_download,b09d6740-4747-4135-9f78-1dc9080e6317�
�
$b09d6740-4747-4135-9f78-1dc9080e6317
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 0 9 d 6 7 4 0 - 4 7 4 7 - 4 1 3 5 - 9 f 7 8 - 1 d c 9 0 8 0 e 6 3 1 7   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 0 9 d 6 7 4 0 - 4 7 4 7 - 4 1 3 5 - 9 f 7 8 - 1 d c 9 0 8 0 e 6 3 1 7   x�����Є����ք��� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     +�q�E�         &9_b09d6740-4747-4135-9f78-1dc9080e6317�	$b09d6740-4747-4135-9f78-1dc9080e6317��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\b09d6740-4747-4135-9f78-1dc9080e63178�������@��¤���HPϟ�/X���`��¤���p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_nSRsc-K_RJDASS4Hcp_cpCwJpkuwzOXz-fA6khrkKCoEWGI7vvPsV0JK_Zi3X_n1ueRRSwQk date:Tue, 27 May 2025 19:49:15 GMT expires:Wed, 28 May 2025 19:49:15 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2�'O�G�         &9_df31e84b-307d-445c-ba63-6c0dceed6fc9�	$df31e84b-307d-445c-ba63-6c0dceed6fc9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\df31e84b-307d-445c-ba63-6c0dceed6fc98�������@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGH��G�         &9_df31e84b-307d-445c-ba63-6c0dceed6fc9�	$df31e84b-307d-445c-ba63-6c0dceed6fc9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\df31e84b-307d-445c-ba63-6c0dceed6fc98�������@ HPϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGċ|/�         021_download,b09d6740-4747-4135-9f78-1dc9080e6317�
�
$b09d6740-4747-4135-9f78-1dc9080e6317
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 0 9 d 6 7 4 0 - 4 7 4 7 - 4 1 3 5 - 9 f 7 8 - 1 d c 9 0 8 0 e 6 3 1 7   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 0 9 d 6 7 4 0 - 4 7 4 7 - 4 1 3 5 - 9 f 7 8 - 1 d c 9 0 8 0 e 6 3 1 7   x�����Є����ք��� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     ����> �          021_download,b09d6740-4747-4135-9f78-1dc9080e6317aɀ���         021_download,fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�
�
$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x ��ք������������� �� � � � � � � ����������������������


     �nJ���         &9_fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�	$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\fe1335f1-44c2-4e1c-8b18-b267bde3ddaa8ȑ�����@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_iKZpNgGazGJ4nhtjPhBtHZQakRTqDMvtw_8XvoZZJWfetjGgJsjhfsynu6SUyCQDwSyGx9e4 accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 19:49:16 GMT expires:Wed, 28 May 2025 19:49:16 GMT x-goog-hash:crc32c=HZgoPg== content-length:45166 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSi1�G�         021_download,fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�
�
$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   9 0 8 4 2 6 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ f e 1 3 3 5 f 1 - 4 4 c 2 - 4 e 1 c - 8 b 1 8 - b 2 6 7 b d e 3 d d a a   x ��ք������������� �� �� � � � � ����������������������


     [�v&K�         021_download,fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�
�
$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ f e 1 3 3 5 f 1 - 4 4 c 2 - 4 e 1 c - 8 b 1 8 - b 2 6 7 b d e 3 d d a a   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ f e 1 3 3 5 f 1 - 4 4 c 2 - 4 e 1 c - 8 b 1 8 - b 2 6 7 b d e 3 d d a a   x����ք����؄��� �� ��0{n��3=�����P��r�w@��à��� � � � � ����������������������


     ��/7��         &9_fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�	$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\fe1335f1-44c2-4e1c-8b18-b267bde3ddaa8ȑ�����@��Ф���HPϟ�/X��`��Ф���p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_iKZpNgGazGJ4nhtjPhBtHZQakRTqDMvtw_8XvoZZJWfetjGgJsjhfsynu6SUyCQDwSyGx9e4 accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 27 May 2025 19:49:16 GMT expires:Wed, 28 May 2025 19:49:16 GMT x-goog-hash:crc32c=HZgoPg== content-length:45166 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS�7EB)�         021_download,fe1335f1-44c2-4e1c-8b18-b267bde3ddaa�
�
$fe1335f1-44c2-4e1c-8b18-b267bde3ddaa
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ f e 1 3 3 5 f 1 - 4 4 c 2 - 4 e 1 c - 8 b 1 8 - b 2 6 7 b d e 3 d d a a   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f�#�" i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ f e 1 3 3 5 f 1 - 4 4 c 2 - 4 e 1 c - 8 b 1 8 - b 2 6 7 b d e 3 d d a a   x����ք����؄��� �� ��0{n��3=�����P��r�w@��à��� � � � � ����������������������


     �H�T> �          021_download,fe1335f1-44c2-4e1c-8b18-b267bde3ddaaY-{h��         021_download,df31e84b-307d-445c-ba63-6c0dceed6fc9�
�
$df31e84b-307d-445c-ba63-6c0dceed6fc9
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ��℠������������ �� � � � � � � ����������������������


     c��X�         &9_df31e84b-307d-445c-ba63-6c0dceed6fc9�	$df31e84b-307d-445c-ba63-6c0dceed6fc9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\df31e84b-307d-445c-ba63-6c0dceed6fc98�������@ HPϟ�/X ` p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH891i3a4cjvAUb1v2_f_DqWHMuAsMPKMjyulGaTUhzra6m0FePQGHrmHON8Es6bCFz1p1E_8Umo date:Tue, 27 May 2025 19:49:17 GMT expires:Wed, 28 May 2025 19:49:17 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=yvBA+g== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGL��_�         021_download,df31e84b-307d-445c-ba63-6c0dceed6fc9�
�
$df31e84b-307d-445c-ba63-6c0dceed6fc9
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   3 7 8 3 6 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d f 3 1 e 8 4 b - 3 0 7 d - 4 4 5 c - b a 6 3 - 6 c 0 d c e e d 6 f c 9   x ��℠������������ �� �� � � � � ����������������������


     ��h�>�         021_download,df31e84b-307d-445c-ba63-6c0dceed6fc9�
�
$df31e84b-307d-445c-ba63-6c0dceed6fc9
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d f 3 1 e 8 4 b - 3 0 7 d - 4 4 5 c - b a 6 3 - 6 c 0 d c e e d 6 f c 9   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d f 3 1 e 8 4 b - 3 0 7 d - 4 4 5 c - b a 6 3 - 6 c 0 d c e e d 6 f c 9   x�֧��℠���脠�� a�"�*�gfI�9��MG�������ȁ��W�~��(���� � � � � ����������������������


     _��i�         &9_df31e84b-307d-445c-ba63-6c0dceed6fc9�	$df31e84b-307d-445c-ba63-6c0dceed6fc9��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\df31e84b-307d-445c-ba63-6c0dceed6fc98�������@�ѥ���HPϟ�/X�֧`�ѥ���p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH891i3a4cjvAUb1v2_f_DqWHMuAsMPKMjyulGaTUhzra6m0FePQGHrmHON8Es6bCFz1p1E_8Umo date:Tue, 27 May 2025 19:49:17 GMT expires:Wed, 28 May 2025 19:49:17 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=yvBA+g== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGJ���>�         021_download,df31e84b-307d-445c-ba63-6c0dceed6fc9�
�
$df31e84b-307d-445c-ba63-6c0dceed6fc9
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d f 3 1 e 8 4 b - 3 0 7 d - 4 4 5 c - b a 6 3 - 6 c 0 d c e e d 6 f c 9   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d f 3 1 e 8 4 b - 3 0 7 d - 4 4 5 c - b a 6 3 - 6 c 0 d c e e d 6 f c 9   x�֧��℠���脠�� a�"�*�gfI�9��MG�������ȁ��W�~��(���� � � � � ����������������������


     �wR> �          021_download,df31e84b-307d-445c-ba63-6c0dceed6fc9���� �          ���� �          ���� �          �u_:� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�o"��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10Ֆ0	 �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�Ms� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��AN` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�
M���         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�X�6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10h�da�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10Ԙ��W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10J6�� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10��i�d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��Q �          �w}�- �         22_3|640x720|30<  9���4qyB���� �          ���� �          ���� �          ���@� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�����         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10	)�� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10a�J� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10$3��` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��;��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10v�!6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�108]�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�?�W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10��1� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�1016!d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��� �          ]��l. �         22_1|360x640|30�  9�]#5qyBln�� �          ln�� �          ln�� �          =ǪВ �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�R��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�109EcH� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10j}�` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10 ⸐��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10u9n6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10H���a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10t`)W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�<�S� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10���'��         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315 �Bz�';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10���C �          