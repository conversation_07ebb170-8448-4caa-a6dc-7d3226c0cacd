��C �          ��C �          ��C �          ��C �          gm�� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10��k͐�         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10ɾ�* �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�0N�� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10K�G` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10m�,��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10h���6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�.թa�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10��jW�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�~�.� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10����d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�p�1 �          �p�1 �          �p�1 �          �p�1 �          �׹_� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10j�Z��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�109vV� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10���>� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��5` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10����         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10l��6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10 ���a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�106Ah4W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�109��u� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�e�d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�IH� �          �IH� �          �IH� �          �IH� �          99G�� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10㤃���         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10T[�� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�102��� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�
&}` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��6��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10A�6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10��]a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10����W          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10Ӈ��          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10&�2	         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� p	H�[ "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��I�           ��I�           ��I�           ��I�           :�C��          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10l蜶�         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10��           
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�<��          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�.��`          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10���ӂ         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10n@�6	         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10 89-a
         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10���bW         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�100�2��          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10?��d
         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10;G\           ;G\           ;G\           ;G\           �ل��          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10M�Ґ         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10T�*��          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��G}`          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10'�%�         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10���6         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�b�a         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10'U_vW         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�,^�          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�fK�d         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10rF�           rF�           rF�           rF�           d��^�          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10[��F�         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10��ɞ          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10��tB�          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10���`          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��5�         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�t]�6         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�/��a          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10���W!         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10���� "         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10UN߃�#         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on��<�� _site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�y.� $          �y.� $          �y.� $          �y.� $          �g9� $         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10mq��%         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�f� &         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�Q��� '         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10.I�n` (         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��h+�)         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10� P@6*         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�psa+         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�N�W,         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10���� -         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�td.         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�k /          [�2. /         22_1|480x720|40� 9����;qyB,���. 0         22_1|480x720|40�
 9�.��;qyB��1�. 1         22_1|480x720|40�
 97#k�;qyBUS�. 2         22_1|480x720|40� 9LmH <qyB��u�. 3         22_1|480x720|40�	 9g%<qyB!3. 4         22_1|480x720|40� 9�N<qyB4X$: 5         #38_e       ���w���   F�Y   F�Y
 ��*d�i 6          *d�i 6          *d�i 6          dE� 6         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10\gKڐ7         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10csT 8         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10|��� 9         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10����` :         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�/,��;         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�)-&6<         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10��Ua=         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10��W>         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10,D��� ?         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10͐�Nd@         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�!1 A          �!1 A          �!1 A          �!1 A          sN��� A         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�CS�B         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10Y7�� C         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10��� D         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�Ós` E         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10>q�b�F         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10Z�	�6G         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10��cvaH         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10x�j�WI         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�ZH2� J         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10��� �K         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCY�Ae�T url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�*� L          �*� L          �*� L          �*� L          �pFɒ L         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�����M         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10؆PR N         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10)��g� O         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10#v<�` P         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10kk��Q         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10yO{6R         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10hCDcaS         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10���WT         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10xʳ� U         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10U �dV         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�100�|r W          0�|r W          0�|r W          0�|r W          �snj� W         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10���,�X         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�102тm Y         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10e-C� Z         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�U��` [         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10"fF��\         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10P�)6]         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�?�Ra^         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�|ͮW_         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�Qz`� `         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10��yfda         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10���� b          ���� b          ���� b          ���� b          ��$� b         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10��c         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�L�� d         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10���� e         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�wԙ` f         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10eH\�g         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10J[4�6h         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10 �=,ai         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�Ԇ^Wj         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10h3��� k         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10f�R�dl         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A46687915Ofb 7E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10�$3� m          �$3� m          �$3� m          �$3� m          T�UX� m         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10���n�n         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10J$W o         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�In�� p         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10Ǳ
c` q         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�b~Y�r         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10N1I�6s         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�Gz~at         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10����Wu         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10� � v         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10a�Q�dw         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10h��� x          h��� x          h��� x          h��� x          ���� x         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�Z"�y         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10G��) z         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10B���� {         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10<��+` |         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10y��V�}         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10ceY�6~         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10 ඌa         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�|�W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10	��� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10s���d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10.?zB �          .?zB �          .?zB �          .?zB �          *�E!� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10H/e��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10M��@ �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10���� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�8K` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�106�#g��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10[L6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10��4Na�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�P2�W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10j�z� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�Xw*�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSE���bLECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10֌x� �          ֌x� �          ֌x� �          ֌x� �          ��� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10i~N���         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10@NoP �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10k9��� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10���` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10X;��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10)���6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10(y[ia�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10U�קW�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�eq#� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�'xd�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10ߩut �          ߩut �          ߩut �          ߩut �          ����� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10?-o��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�&�� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10߇��� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10�3y` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��	��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�BZ6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10 ̱ba�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10���W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�{��� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10G
\�d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�103I �          3I �          3I �          3I �          �Njؒ �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10|&�Ő�         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�v �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10��|� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10.;k�` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�
���         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10��+n6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10�3m�a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10K�qW�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�r�u� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10݁V���         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERc˶�E metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10��\� �          ��\� �          ��\� �          ��\� �          ���� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10*��Y��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10�3Y �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�N�� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10~>�[` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�Z���         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10^s�+6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10 �*Fa�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�l��W�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10Oq_� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10�{��d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10\g�� �          \g�� �          \g�� �          \g�� �          ��d� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10�(�5��         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10s�� �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10�/_� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10~t�` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10�!����         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10���6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�100���a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10���kW�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�U
�� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10%�J�d�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10)��P �          )��P �          )��P �          )��P �          @�
$� �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10ٚ ���         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10i�qK �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�10��{\� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��X6` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10)0����         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10oP'�6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�10��2/a�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�Q�UW�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10�М|� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10;�>�         37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ��a&�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ٖ�10G�k� �          G�k� �          G�k� �          G�k� �          G�k� �          ���ޒ �         
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ٖ�10p����         
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ���Ҫ��$


   ?ShoppingUserOther  (�ٖ�10d�= �         
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ٖ�105��C� �         
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ٖ�10��Ð` �         
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ٖ�10��p��         
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ٖ�10�ڃb6�         37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ٖ�100�Ba�         37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ٖ�10�YW�         37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ٖ�10^h�� �         37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ٖ�10